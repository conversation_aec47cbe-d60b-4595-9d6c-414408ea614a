// HistoryAnalysisExample.tsx - Exemple d'utilisation de la fonctionnalité d'analyse historique
import React, { useState } from 'react';
import HistoryAnalysisSection from '../components/imageUp/HistoryAnalysisSection';
import { ClassData } from '../services/HistoryAnalysisService';

const HistoryAnalysisExample: React.FC = () => {
  const [darkMode, setDarkMode] = useState(false);
  const [importedData, setImportedData] = useState<ClassData | null>(null);

  // Exemple de données de classe actuelle
  const currentClassData: ClassData = {
    name: 'Client',
    attributes: [
      '- id: number',
      '- dateCreation: Date'
    ],
    methods: [
      '+ save(): void',
      '+ delete(): boolean'
    ]
  };

  const handleImport = (data: ClassData) => {
    setImportedData(data);
    console.log('Données importées:', data);
  };

  const containerStyle = {
    padding: '20px',
    minHeight: '100vh',
    backgroundColor: darkMode ? '#0f172a' : '#ffffff',
    color: darkMode ? '#f8fafc' : '#0f172a',
    fontFamily: 'system-ui, -apple-system, sans-serif'
  };

  const cardStyle = {
    backgroundColor: darkMode ? '#1e293b' : '#f8fafc',
    border: `1px solid ${darkMode ? '#334155' : '#e2e8f0'}`,
    borderRadius: '12px',
    padding: '24px',
    marginBottom: '24px',
    boxShadow: darkMode 
      ? '0 4px 6px -1px rgba(0, 0, 0, 0.3)' 
      : '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
  };

  const buttonStyle = {
    backgroundColor: darkMode ? '#3b82f6' : '#2563eb',
    color: '#ffffff',
    border: 'none',
    borderRadius: '8px',
    padding: '8px 16px',
    cursor: 'pointer',
    fontSize: '14px',
    fontWeight: '600',
    marginBottom: '20px'
  };

  const codeStyle = {
    backgroundColor: darkMode ? '#0f172a' : '#f1f5f9',
    border: `1px solid ${darkMode ? '#334155' : '#cbd5e1'}`,
    borderRadius: '6px',
    padding: '12px',
    fontFamily: 'Monaco, Consolas, monospace',
    fontSize: '13px',
    whiteSpace: 'pre-wrap' as const,
    overflow: 'auto'
  };

  return (
    <div style={containerStyle}>
      <div style={{ maxWidth: '800px', margin: '0 auto' }}>
        <h1 style={{ textAlign: 'center', marginBottom: '32px' }}>
          Exemple d'Analyse Historique
        </h1>

        <button 
          style={buttonStyle}
          onClick={() => setDarkMode(!darkMode)}
        >
          {darkMode ? '☀️ Mode Clair' : '🌙 Mode Sombre'}
        </button>

        <div style={cardStyle}>
          <h2 style={{ marginTop: 0, marginBottom: '16px' }}>
            Classe en cours d'édition : "{currentClassData.name}"
          </h2>
          
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '24px' }}>
            <div>
              <h3 style={{ fontSize: '14px', fontWeight: '600', marginBottom: '8px' }}>
                Attributs actuels:
              </h3>
              <div style={codeStyle}>
                {currentClassData.attributes.join('\n')}
              </div>
            </div>
            <div>
              <h3 style={{ fontSize: '14px', fontWeight: '600', marginBottom: '8px' }}>
                Méthodes actuelles:
              </h3>
              <div style={codeStyle}>
                {currentClassData.methods.join('\n')}
              </div>
            </div>
          </div>

          {/* Composant d'analyse historique */}
          <HistoryAnalysisSection
            darkMode={darkMode}
            targetClassName={currentClassData.name}
            currentClassData={currentClassData}
            onImport={handleImport}
          />
        </div>

        {importedData && (
          <div style={cardStyle}>
            <h2 style={{ marginTop: 0, marginBottom: '16px', color: '#10b981' }}>
              ✅ Données importées avec succès !
            </h2>
            
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
              <div>
                <h3 style={{ fontSize: '14px', fontWeight: '600', marginBottom: '8px' }}>
                  Attributs fusionnés ({importedData.attributes.length}):
                </h3>
                <div style={codeStyle}>
                  {importedData.attributes.join('\n')}
                </div>
              </div>
              <div>
                <h3 style={{ fontSize: '14px', fontWeight: '600', marginBottom: '8px' }}>
                  Méthodes fusionnées ({importedData.methods.length}):
                </h3>
                <div style={codeStyle}>
                  {importedData.methods.join('\n')}
                </div>
              </div>
            </div>

            <div style={{ 
              marginTop: '16px', 
              padding: '12px', 
              backgroundColor: darkMode ? 'rgba(16, 185, 129, 0.1)' : 'rgba(16, 185, 129, 0.05)',
              border: `1px solid ${darkMode ? 'rgba(16, 185, 129, 0.3)' : 'rgba(16, 185, 129, 0.2)'}`,
              borderRadius: '6px',
              fontSize: '13px'
            }}>
              <strong>Résumé de l'import:</strong>
              <br />
              • {importedData.attributes.length - currentClassData.attributes.length} nouveaux attributs ajoutés
              <br />
              • {importedData.methods.length - currentClassData.methods.length} nouvelles méthodes ajoutées
            </div>
          </div>
        )}

        <div style={cardStyle}>
          <h2 style={{ marginTop: 0, marginBottom: '16px' }}>
            Instructions d'utilisation
          </h2>
          
          <ol style={{ lineHeight: '1.6' }}>
            <li>
              <strong>Analyse automatique</strong> : La section "Analyse Historique" recherche automatiquement 
              les diagrammes contenant une classe nommée "{currentClassData.name}"
            </li>
            <li>
              <strong>Expansion</strong> : Cliquez sur l'en-tête pour étendre/rétracter la section
            </li>
            <li>
              <strong>Tri</strong> : Utilisez le menu déroulant pour trier par similarité, date ou nom
            </li>
            <li>
              <strong>Sélection</strong> : Cochez les diagrammes sources que vous souhaitez importer
            </li>
            <li>
              <strong>Aperçu</strong> : Cliquez sur l'icône œil pour voir un aperçu de la classe
            </li>
            <li>
              <strong>Import</strong> : Cliquez sur "Importer" pour fusionner les données sélectionnées
            </li>
            <li>
              <strong>Conflits</strong> : Si des conflits sont détectés, un modal s'ouvrira pour les résoudre
            </li>
          </ol>

          <div style={{ 
            marginTop: '16px', 
            padding: '12px', 
            backgroundColor: darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)',
            border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.3)' : 'rgba(59, 130, 246, 0.2)'}`,
            borderRadius: '6px',
            fontSize: '13px'
          }}>
            <strong>💡 Astuce:</strong> Les diagrammes sont marqués avec des icônes pour indiquer 
            leur niveau d'accès (🛡️ vôtre, 👥 partagé, 👁️ public, 🔒 restreint)
          </div>
        </div>
      </div>
    </div>
  );
};

export default HistoryAnalysisExample;
