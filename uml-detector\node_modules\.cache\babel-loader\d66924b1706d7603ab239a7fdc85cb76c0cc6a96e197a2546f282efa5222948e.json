{"ast": null, "code": "import count from \"../count.js\";\nexport default function (values) {\n  return Math.ceil(Math.log(count(values)) / Math.LN2) + 1;\n}", "map": {"version": 3, "names": ["count", "values", "Math", "ceil", "log", "LN2"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/node_modules/d3-sankey/node_modules/d3-array/src/threshold/sturges.js"], "sourcesContent": ["import count from \"../count.js\";\n\nexport default function(values) {\n  return Math.ceil(Math.log(count(values)) / Math.LN2) + 1;\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAE/B,eAAe,UAASC,MAAM,EAAE;EAC9B,OAAOC,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAACJ,KAAK,CAACC,MAAM,CAAC,CAAC,GAAGC,IAAI,CAACG,GAAG,CAAC,GAAG,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}