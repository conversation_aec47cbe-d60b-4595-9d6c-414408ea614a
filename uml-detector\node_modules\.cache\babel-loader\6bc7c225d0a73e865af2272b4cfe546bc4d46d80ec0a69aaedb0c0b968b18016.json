{"ast": null, "code": "/******************************************************************************\n * Copyright 2023 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nexport * from './caching.js';\nexport * from './event.js';\nexport * from './collections.js';\nexport * from './disposable.js';\nexport * from './errors.js';\nexport * from './grammar-loader.js';\nexport * from './promise-utils.js';\nexport * from './stream.js';\nexport * from './uri-utils.js';\nimport * as AstUtils from './ast-utils.js';\nimport * as Cancellation from './cancellation.js';\nimport * as CstUtils from './cst-utils.js';\nimport * as GrammarUtils from './grammar-utils.js';\nimport * as RegExpUtils from './regexp-utils.js';\nexport { AstUtils, Cancellation, CstUtils, GrammarUtils, RegExpUtils };", "map": {"version": 3, "names": ["AstU<PERSON><PERSON>", "Cancellation", "CstUtils", "GrammarUtils", "RegExpUtils"], "sources": ["C:\\Users\\<USER>\\FixTorchUMLDGM\\node_modules\\langium\\src\\utils\\index.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2023 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nexport * from './caching.js';\r\nexport * from './event.js';\r\nexport * from './collections.js';\r\nexport * from './disposable.js';\r\nexport * from './errors.js';\r\nexport * from './grammar-loader.js';\r\nexport * from './promise-utils.js';\r\nexport * from './stream.js';\r\nexport * from './uri-utils.js';\r\n\r\nimport * as AstUtils from './ast-utils.js';\r\nimport * as Cancellation from './cancellation.js';\r\nimport * as CstUtils from './cst-utils.js';\r\nimport * as GrammarUtils from './grammar-utils.js';\r\nimport * as RegExpUtils from './regexp-utils.js';\r\nexport { AstUtils, Cancellation, CstUtils, GrammarUtils, RegExpUtils };\r\n"], "mappings": "AAAA;;;;;AAMA,cAAc,cAAc;AAC5B,cAAc,YAAY;AAC1B,cAAc,kBAAkB;AAChC,cAAc,iBAAiB;AAC/B,cAAc,aAAa;AAC3B,cAAc,qBAAqB;AACnC,cAAc,oBAAoB;AAClC,cAAc,aAAa;AAC3B,cAAc,gBAAgB;AAE9B,OAAO,KAAKA,QAAQ,MAAM,gBAAgB;AAC1C,OAAO,KAAKC,YAAY,MAAM,mBAAmB;AACjD,OAAO,KAAKC,QAAQ,MAAM,gBAAgB;AAC1C,OAAO,KAAKC,YAAY,MAAM,oBAAoB;AAClD,OAAO,KAAKC,WAAW,MAAM,mBAAmB;AAChD,SAASJ,QAAQ,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}