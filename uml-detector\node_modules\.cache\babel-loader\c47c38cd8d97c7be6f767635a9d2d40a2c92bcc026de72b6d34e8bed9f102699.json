{"ast": null, "code": "// EntityPopupStyles.ts \nexport const getEntityPopupStyles = darkMode => ({\n  overlay: {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    zIndex: 1000,\n    backdropFilter: 'blur(8px)',\n    WebkitBackdropFilter: 'blur(8px)',\n    animation: 'fadeIn 0.3s ease-out'\n  },\n  popup: {\n    backgroundColor: darkMode ? '#0a0f1c' : '#ffffff',\n    borderRadius: '0',\n    // Coins droits pour tout le popup\n    boxShadow: darkMode ? '0 25px 50px -12px rgba(0, 0, 0, 0.8), 0 0 0 1px rgba(59, 130, 246, 0.1), 0 0 20px rgba(59, 130, 246, 0.05)' : '0 25px 50px -12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(59, 130, 246, 0.05), 0 0 20px rgba(59, 130, 246, 0.02)',\n    padding: '0',\n    minWidth: '520px',\n    maxWidth: '700px',\n    maxHeight: '85vh',\n    border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n    margin: '20px',\n    position: 'relative',\n    animation: 'slideIn 0.3s ease-out',\n    display: 'flex',\n    flexDirection: 'column'\n  },\n  header: {\n    display: 'flex',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    padding: '24px 28px 20px',\n    borderBottom: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}`,\n    background: darkMode ? 'linear-gradient(135deg, #0a0f1c 0%, #1e293b 50%, #0f172a 100%)' : 'linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%)',\n    borderRadius: '0',\n    // Coins droits pour le header\n    position: 'relative',\n    flexShrink: 0\n  },\n  headerGlow: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n    height: '2px',\n    background: darkMode ? 'linear-gradient(90deg, transparent, #3b82f6, transparent)' : 'linear-gradient(90deg, transparent, #3b82f6, transparent)',\n    borderRadius: '0' // Coins droits pour le headerGlow\n  },\n  title: {\n    margin: 0,\n    fontSize: '22px',\n    fontWeight: '700',\n    color: darkMode ? '#f8fafc' : '#0f172a',\n    letterSpacing: '-0.025em',\n    background: darkMode ? 'linear-gradient(135deg, #f8fafc 0%, #cbd5e1 100%)' : 'linear-gradient(135deg, #0f172a 0%, #374151 100%)',\n    WebkitBackgroundClip: 'text',\n    WebkitTextFillColor: 'transparent',\n    backgroundClip: 'text'\n  },\n  subtitle: {\n    fontSize: '14px',\n    color: darkMode ? '#94a3b8' : '#64748b',\n    fontWeight: '500',\n    marginTop: '6px',\n    opacity: 0.9\n  },\n  aiIndicator: {\n    display: 'flex',\n    alignItems: 'center',\n    gap: '8px',\n    fontSize: '12px',\n    color: darkMode ? '#60a5fa' : '#3b82f6',\n    fontWeight: '600',\n    marginTop: '4px',\n    textTransform: 'uppercase',\n    letterSpacing: '0.05em'\n  },\n  aiDot: {\n    width: '6px',\n    height: '6px',\n    borderRadius: '50%',\n    backgroundColor: darkMode ? '#60a5fa' : '#3b82f6',\n    animation: 'pulse 2s infinite'\n  },\n  closeButton: {\n    background: darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)',\n    border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n    cursor: 'pointer',\n    color: darkMode ? '#cbd5e1' : '#64748b',\n    fontSize: '16px',\n    padding: '10px',\n    borderRadius: '10px',\n    transition: 'all 0.2s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    width: '36px',\n    height: '36px',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)'\n  },\n  content: {\n    padding: '28px 28px 120px 28px',\n    // Plus d'espace en bas pour les boutons\n    background: darkMode ? 'linear-gradient(180deg, #0a0f1c 0%, #1e293b 100%)' : 'linear-gradient(180deg, #ffffff 0%, #f8fafc 100%)',\n    overflowY: 'auto',\n    flex: 1,\n    minHeight: 0 // Permet au flex de fonctionner correctement\n  },\n  tableContainer: {\n    marginBottom: '24px',\n    borderRadius: '12px',\n    overflow: 'hidden',\n    border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}`,\n    background: darkMode ? 'rgba(30, 41, 59, 0.5)' : 'rgba(248, 250, 252, 0.5)',\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)'\n  },\n  table: {\n    width: '100%',\n    borderCollapse: 'collapse',\n    fontSize: '14px'\n  },\n  tableHeader: {\n    backgroundColor: darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)',\n    borderBottom: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)'\n  },\n  tableHeaderCell: {\n    padding: '18px 24px',\n    textAlign: 'left',\n    fontWeight: '600',\n    color: darkMode ? '#e2e8f0' : '#374151',\n    fontSize: '13px',\n    textTransform: 'uppercase',\n    letterSpacing: '0.05em',\n    width: '50%',\n    position: 'relative'\n  },\n  tableBody: {\n    backgroundColor: 'transparent'\n  },\n  tableRow: {\n    borderBottom: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.08)' : 'rgba(59, 130, 246, 0.05)'}`,\n    minHeight: '200px',\n    transition: 'all 0.2s ease',\n    '&:hover': {\n      backgroundColor: darkMode ? 'rgba(59, 130, 246, 0.05)' : 'rgba(59, 130, 246, 0.02)'\n    }\n  },\n  tableCell: {\n    padding: '24px',\n    verticalAlign: 'top',\n    color: darkMode ? '#cbd5e1' : '#4b5563',\n    fontSize: '14px',\n    lineHeight: '1.7',\n    width: '50%'\n  },\n  emptyState: {\n    textAlign: 'center',\n    color: darkMode ? '#64748b' : '#9ca3af',\n    fontStyle: 'italic',\n    fontSize: '14px',\n    padding: '48px 24px',\n    background: darkMode ? 'radial-gradient(circle at center, rgba(59, 130, 246, 0.05) 0%, transparent 70%)' : 'radial-gradient(circle at center, rgba(59, 130, 246, 0.02) 0%, transparent 70%)'\n  },\n  buttonContainer: {\n    display: 'flex',\n    gap: '16px',\n    padding: '20px 28px 24px 28px',\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    right: 0,\n    background: darkMode ? 'linear-gradient(180deg, rgba(10, 15, 28, 0.9) 0%, #0a0f1c 100%)' : 'linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, #ffffff 100%)',\n    borderTop: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}`,\n    borderRadius: '0',\n    // Coins droits pour le container des boutons\n    backdropFilter: 'blur(20px)',\n    WebkitBackdropFilter: 'blur(20px)',\n    boxShadow: darkMode ? '0 -4px 20px rgba(0, 0, 0, 0.3)' : '0 -4px 20px rgba(0, 0, 0, 0.1)'\n  },\n  modifyButton: {\n    background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n    color: '#ffffff',\n    border: 'none',\n    borderRadius: '12px',\n    padding: '14px 28px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    flex: 1,\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    gap: '10px',\n    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',\n    position: 'relative',\n    overflow: 'hidden'\n  },\n  cancelButton: {\n    backgroundColor: 'transparent',\n    color: darkMode ? '#94a3b8' : '#64748b',\n    border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\n    borderRadius: '12px',\n    padding: '14px 28px',\n    fontSize: '14px',\n    fontWeight: '600',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease',\n    flex: 1,\n    backdropFilter: 'blur(10px)',\n    WebkitBackdropFilter: 'blur(10px)'\n  },\n  // Styles pour les checkboxes\n  checkboxContainer: {\n    display: 'flex',\n    alignItems: 'center',\n    marginBottom: '12px',\n    fontSize: '14px',\n    color: darkMode ? '#cbd5e1' : '#4b5563',\n    padding: '8px 0',\n    borderRadius: '8px',\n    transition: 'all 0.2s ease'\n  },\n  checkbox: {\n    marginRight: '12px',\n    cursor: 'pointer',\n    width: '18px',\n    height: '18px',\n    accentColor: '#3b82f6'\n  },\n  checkboxLabel: {\n    cursor: 'pointer',\n    flex: 1,\n    lineHeight: '1.5'\n  },\n  // Animations CSS\n  '@keyframes fadeIn': {\n    from: {\n      opacity: 0\n    },\n    to: {\n      opacity: 1\n    }\n  },\n  '@keyframes slideIn': {\n    from: {\n      opacity: 0,\n      transform: 'translateY(-20px) scale(0.95)'\n    },\n    to: {\n      opacity: 1,\n      transform: 'translateY(0) scale(1)'\n    }\n  },\n  '@keyframes pulse': {\n    '0%, 100%': {\n      opacity: 1\n    },\n    '50%': {\n      opacity: 0.5\n    }\n  },\n  // Styles pour les états hover\n  modifyButtonHover: {\n    background: 'linear-gradient(135deg, #2563eb 0%, #1e40af 100%)',\n    transform: 'translateY(-2px)',\n    boxShadow: '0 8px 20px rgba(59, 130, 246, 0.4)'\n  },\n  cancelButtonHover: {\n    backgroundColor: darkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)',\n    borderColor: darkMode ? 'rgba(59, 130, 246, 0.3)' : 'rgba(59, 130, 246, 0.2)'\n  },\n  closeButtonHover: {\n    backgroundColor: darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)',\n    transform: 'scale(1.05)'\n  }\n});", "map": {"version": 3, "names": ["getEntityPopupStyles", "darkMode", "overlay", "position", "top", "left", "right", "bottom", "backgroundColor", "display", "alignItems", "justifyContent", "zIndex", "<PERSON><PERSON>ilter", "WebkitBackdropFilter", "animation", "popup", "borderRadius", "boxShadow", "padding", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "maxHeight", "border", "margin", "flexDirection", "header", "borderBottom", "background", "flexShrink", "headerGlow", "height", "title", "fontSize", "fontWeight", "color", "letterSpacing", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "subtitle", "marginTop", "opacity", "aiIndicator", "gap", "textTransform", "aiDot", "width", "closeButton", "cursor", "transition", "content", "overflowY", "flex", "minHeight", "tableContainer", "marginBottom", "overflow", "table", "borderCollapse", "tableHeader", "tableHeaderCell", "textAlign", "tableBody", "tableRow", "tableCell", "verticalAlign", "lineHeight", "emptyState", "fontStyle", "buttonContainer", "borderTop", "modifyButton", "cancelButton", "checkboxContainer", "checkbox", "marginRight", "accentColor", "checkboxLabel", "from", "to", "transform", "modifyButtonHover", "cancelButtonHover", "borderColor", "closeButtonHover"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/components/imageUp/EntityPopupStyles.ts"], "sourcesContent": ["// EntityPopupStyles.ts \r\nexport const getEntityPopupStyles = (darkMode: boolean) => ({\r\n  overlay: {\r\n    position: 'fixed' as const,\r\n    top: 0,\r\n    left: 0,\r\n    right: 0,\r\n    bottom: 0,\r\n    backgroundColor: 'rgba(0, 0, 0, 0.6)',\r\n    display: 'flex',\r\n    alignItems: 'center',\r\n    justifyContent: 'center',\r\n    zIndex: 1000,\r\n    backdropFilter: 'blur(8px)',\r\n    WebkitBackdropFilter: 'blur(8px)',\r\n    animation: 'fadeIn 0.3s ease-out',\r\n  },\r\n  \r\n  popup: {\r\n    backgroundColor: darkMode ? '#0a0f1c' : '#ffffff',\r\n    borderRadius: '0', // Coins droits pour tout le popup\r\n    boxShadow: darkMode \r\n      ? '0 25px 50px -12px rgba(0, 0, 0, 0.8), 0 0 0 1px rgba(59, 130, 246, 0.1), 0 0 20px rgba(59, 130, 246, 0.05)' \r\n      : '0 25px 50px -12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(59, 130, 246, 0.05), 0 0 20px rgba(59, 130, 246, 0.02)',\r\n    padding: '0',\r\n    minWidth: '520px',\r\n    maxWidth: '700px',\r\n    maxHeight: '85vh',\r\n    border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\r\n    margin: '20px',\r\n    position: 'relative' as const,\r\n    animation: 'slideIn 0.3s ease-out',\r\n    display: 'flex',\r\n    flexDirection: 'column' as const,\r\n  },\r\n  \r\n  header: {\r\n    display: 'flex',\r\n    justifyContent: 'space-between',\r\n    alignItems: 'center',\r\n    padding: '24px 28px 20px',\r\n    borderBottom: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}`,\r\n    background: darkMode \r\n      ? 'linear-gradient(135deg, #0a0f1c 0%, #1e293b 50%, #0f172a 100%)' \r\n      : 'linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%)',\r\n    borderRadius: '0', // Coins droits pour le header\r\n    position: 'relative' as const,\r\n    flexShrink: 0,\r\n  },\r\n  \r\n  headerGlow: {\r\n    position: 'absolute' as const,\r\n    top: 0,\r\n    left: 0,\r\n    right: 0,\r\n    height: '2px',\r\n    background: darkMode \r\n      ? 'linear-gradient(90deg, transparent, #3b82f6, transparent)' \r\n      : 'linear-gradient(90deg, transparent, #3b82f6, transparent)',\r\n    borderRadius: '0', // Coins droits pour le headerGlow\r\n  },\r\n  \r\n  title: {\r\n    margin: 0,\r\n    fontSize: '22px',\r\n    fontWeight: '700',\r\n    color: darkMode ? '#f8fafc' : '#0f172a',\r\n    letterSpacing: '-0.025em',\r\n    background: darkMode \r\n      ? 'linear-gradient(135deg, #f8fafc 0%, #cbd5e1 100%)' \r\n      : 'linear-gradient(135deg, #0f172a 0%, #374151 100%)',\r\n    WebkitBackgroundClip: 'text',\r\n    WebkitTextFillColor: 'transparent',\r\n    backgroundClip: 'text',\r\n  },\r\n  \r\n  subtitle: {\r\n    fontSize: '14px',\r\n    color: darkMode ? '#94a3b8' : '#64748b',\r\n    fontWeight: '500',\r\n    marginTop: '6px',\r\n    opacity: 0.9,\r\n  },\r\n  \r\n  aiIndicator: {\r\n    display: 'flex',\r\n    alignItems: 'center',\r\n    gap: '8px',\r\n    fontSize: '12px',\r\n    color: darkMode ? '#60a5fa' : '#3b82f6',\r\n    fontWeight: '600',\r\n    marginTop: '4px',\r\n    textTransform: 'uppercase' as const,\r\n    letterSpacing: '0.05em',\r\n  },\r\n  \r\n  aiDot: {\r\n    width: '6px',\r\n    height: '6px',\r\n    borderRadius: '50%',\r\n    backgroundColor: darkMode ? '#60a5fa' : '#3b82f6',\r\n    animation: 'pulse 2s infinite',\r\n  },\r\n  \r\n  closeButton: {\r\n    background: darkMode \r\n      ? 'rgba(59, 130, 246, 0.1)' \r\n      : 'rgba(59, 130, 246, 0.05)',\r\n    border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\r\n    cursor: 'pointer',\r\n    color: darkMode ? '#cbd5e1' : '#64748b',\r\n    fontSize: '16px',\r\n    padding: '10px',\r\n    borderRadius: '10px',\r\n    transition: 'all 0.2s ease',\r\n    display: 'flex',\r\n    alignItems: 'center',\r\n    justifyContent: 'center',\r\n    width: '36px',\r\n    height: '36px',\r\n    backdropFilter: 'blur(10px)',\r\n    WebkitBackdropFilter: 'blur(10px)',\r\n  },\r\n  \r\n  content: {\r\n    padding: '28px 28px 120px 28px', // Plus d'espace en bas pour les boutons\r\n    background: darkMode \r\n      ? 'linear-gradient(180deg, #0a0f1c 0%, #1e293b 100%)' \r\n      : 'linear-gradient(180deg, #ffffff 0%, #f8fafc 100%)',\r\n    overflowY: 'auto' as const,\r\n    flex: 1,\r\n    minHeight: 0, // Permet au flex de fonctionner correctement\r\n  },\r\n  \r\n  tableContainer: {\r\n    marginBottom: '24px',\r\n    borderRadius: '12px',\r\n    overflow: 'hidden',\r\n    border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}`,\r\n    background: darkMode \r\n      ? 'rgba(30, 41, 59, 0.5)' \r\n      : 'rgba(248, 250, 252, 0.5)',\r\n    backdropFilter: 'blur(10px)',\r\n    WebkitBackdropFilter: 'blur(10px)',\r\n  },\r\n  \r\n  table: {\r\n    width: '100%',\r\n    borderCollapse: 'collapse' as const,\r\n    fontSize: '14px',\r\n  },\r\n  \r\n  tableHeader: {\r\n    backgroundColor: darkMode \r\n      ? 'rgba(59, 130, 246, 0.1)' \r\n      : 'rgba(59, 130, 246, 0.05)',\r\n    borderBottom: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\r\n    backdropFilter: 'blur(10px)',\r\n    WebkitBackdropFilter: 'blur(10px)',\r\n  },\r\n  \r\n  tableHeaderCell: {\r\n    padding: '18px 24px',\r\n    textAlign: 'left' as const,\r\n    fontWeight: '600',\r\n    color: darkMode ? '#e2e8f0' : '#374151',\r\n    fontSize: '13px',\r\n    textTransform: 'uppercase' as const,\r\n    letterSpacing: '0.05em',\r\n    width: '50%',\r\n    position: 'relative' as const,\r\n  },\r\n  \r\n  tableBody: {\r\n    backgroundColor: 'transparent',\r\n  },\r\n  \r\n  tableRow: {\r\n    borderBottom: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.08)' : 'rgba(59, 130, 246, 0.05)'}`,\r\n    minHeight: '200px',\r\n    transition: 'all 0.2s ease',\r\n    '&:hover': {\r\n      backgroundColor: darkMode \r\n        ? 'rgba(59, 130, 246, 0.05)' \r\n        : 'rgba(59, 130, 246, 0.02)',\r\n    },\r\n  },\r\n  \r\n  tableCell: {\r\n    padding: '24px',\r\n    verticalAlign: 'top' as const,\r\n    color: darkMode ? '#cbd5e1' : '#4b5563',\r\n    fontSize: '14px',\r\n    lineHeight: '1.7',\r\n    width: '50%',\r\n  },\r\n  \r\n  emptyState: {\r\n    textAlign: 'center' as const,\r\n    color: darkMode ? '#64748b' : '#9ca3af',\r\n    fontStyle: 'italic',\r\n    fontSize: '14px',\r\n    padding: '48px 24px',\r\n    background: darkMode \r\n      ? 'radial-gradient(circle at center, rgba(59, 130, 246, 0.05) 0%, transparent 70%)' \r\n      : 'radial-gradient(circle at center, rgba(59, 130, 246, 0.02) 0%, transparent 70%)',\r\n  },\r\n  \r\n  buttonContainer: {\r\n    display: 'flex',\r\n    gap: '16px',\r\n    padding: '20px 28px 24px 28px',\r\n    position: 'absolute' as const,\r\n    bottom: 0,\r\n    left: 0,\r\n    right: 0,\r\n    background: darkMode \r\n      ? 'linear-gradient(180deg, rgba(10, 15, 28, 0.9) 0%, #0a0f1c 100%)' \r\n      : 'linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, #ffffff 100%)',\r\n    borderTop: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}`,\r\n    borderRadius: '0', // Coins droits pour le container des boutons\r\n    backdropFilter: 'blur(20px)',\r\n    WebkitBackdropFilter: 'blur(20px)',\r\n    boxShadow: darkMode \r\n      ? '0 -4px 20px rgba(0, 0, 0, 0.3)' \r\n      : '0 -4px 20px rgba(0, 0, 0, 0.1)',\r\n  },\r\n  \r\n  modifyButton: {\r\n    background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\r\n    color: '#ffffff',\r\n    border: 'none',\r\n    borderRadius: '12px',\r\n    padding: '14px 28px',\r\n    fontSize: '14px',\r\n    fontWeight: '600',\r\n    cursor: 'pointer',\r\n    transition: 'all 0.3s ease',\r\n    flex: 1,\r\n    display: 'flex',\r\n    alignItems: 'center',\r\n    justifyContent: 'center',\r\n    gap: '10px',\r\n    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',\r\n    position: 'relative' as const,\r\n    overflow: 'hidden' as const,\r\n  },\r\n  \r\n  cancelButton: {\r\n    backgroundColor: 'transparent',\r\n    color: darkMode ? '#94a3b8' : '#64748b',\r\n    border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,\r\n    borderRadius: '12px',\r\n    padding: '14px 28px',\r\n    fontSize: '14px',\r\n    fontWeight: '600',\r\n    cursor: 'pointer',\r\n    transition: 'all 0.3s ease',\r\n    flex: 1,\r\n    backdropFilter: 'blur(10px)',\r\n    WebkitBackdropFilter: 'blur(10px)',\r\n  },\r\n  \r\n  // Styles pour les checkboxes\r\n  checkboxContainer: {\r\n    display: 'flex',\r\n    alignItems: 'center',\r\n    marginBottom: '12px',\r\n    fontSize: '14px',\r\n    color: darkMode ? '#cbd5e1' : '#4b5563',\r\n    padding: '8px 0',\r\n    borderRadius: '8px',\r\n    transition: 'all 0.2s ease',\r\n  },\r\n  \r\n  checkbox: {\r\n    marginRight: '12px',\r\n    cursor: 'pointer',\r\n    width: '18px',\r\n    height: '18px',\r\n    accentColor: '#3b82f6',\r\n  },\r\n  \r\n  checkboxLabel: {\r\n    cursor: 'pointer',\r\n    flex: 1,\r\n    lineHeight: '1.5',\r\n  },\r\n  \r\n  // Animations CSS\r\n  '@keyframes fadeIn': {\r\n    from: { opacity: 0 },\r\n    to: { opacity: 1 },\r\n  },\r\n  \r\n  '@keyframes slideIn': {\r\n    from: { \r\n      opacity: 0,\r\n      transform: 'translateY(-20px) scale(0.95)',\r\n    },\r\n    to: { \r\n      opacity: 1,\r\n      transform: 'translateY(0) scale(1)',\r\n    },\r\n  },\r\n  \r\n  '@keyframes pulse': {\r\n    '0%, 100%': { opacity: 1 },\r\n    '50%': { opacity: 0.5 },\r\n  },\r\n  \r\n  // Styles pour les états hover\r\n  modifyButtonHover: {\r\n    background: 'linear-gradient(135deg, #2563eb 0%, #1e40af 100%)',\r\n    transform: 'translateY(-2px)',\r\n    boxShadow: '0 8px 20px rgba(59, 130, 246, 0.4)',\r\n  },\r\n  \r\n  cancelButtonHover: {\r\n    backgroundColor: darkMode \r\n      ? 'rgba(59, 130, 246, 0.1)' \r\n      : 'rgba(59, 130, 246, 0.05)',\r\n    borderColor: darkMode ? 'rgba(59, 130, 246, 0.3)' : 'rgba(59, 130, 246, 0.2)',\r\n  },\r\n  \r\n  closeButtonHover: {\r\n    backgroundColor: darkMode \r\n      ? 'rgba(59, 130, 246, 0.2)' \r\n      : 'rgba(59, 130, 246, 0.1)',\r\n    transform: 'scale(1.05)',\r\n  },\r\n});"], "mappings": "AAAA;AACA,OAAO,MAAMA,oBAAoB,GAAIC,QAAiB,KAAM;EAC1DC,OAAO,EAAE;IACPC,QAAQ,EAAE,OAAgB;IAC1BC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,eAAe,EAAE,oBAAoB;IACrCC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,MAAM,EAAE,IAAI;IACZC,cAAc,EAAE,WAAW;IAC3BC,oBAAoB,EAAE,WAAW;IACjCC,SAAS,EAAE;EACb,CAAC;EAEDC,KAAK,EAAE;IACLR,eAAe,EAAEP,QAAQ,GAAG,SAAS,GAAG,SAAS;IACjDgB,YAAY,EAAE,GAAG;IAAE;IACnBC,SAAS,EAAEjB,QAAQ,GACf,4GAA4G,GAC5G,8GAA8G;IAClHkB,OAAO,EAAE,GAAG;IACZC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,OAAO;IACjBC,SAAS,EAAE,MAAM;IACjBC,MAAM,EAAE,aAAatB,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB,EAAE;IACvFuB,MAAM,EAAE,MAAM;IACdrB,QAAQ,EAAE,UAAmB;IAC7BY,SAAS,EAAE,uBAAuB;IAClCN,OAAO,EAAE,MAAM;IACfgB,aAAa,EAAE;EACjB,CAAC;EAEDC,MAAM,EAAE;IACNjB,OAAO,EAAE,MAAM;IACfE,cAAc,EAAE,eAAe;IAC/BD,UAAU,EAAE,QAAQ;IACpBS,OAAO,EAAE,gBAAgB;IACzBQ,YAAY,EAAE,aAAa1B,QAAQ,GAAG,0BAA0B,GAAG,0BAA0B,EAAE;IAC/F2B,UAAU,EAAE3B,QAAQ,GAChB,gEAAgE,GAChE,gEAAgE;IACpEgB,YAAY,EAAE,GAAG;IAAE;IACnBd,QAAQ,EAAE,UAAmB;IAC7B0B,UAAU,EAAE;EACd,CAAC;EAEDC,UAAU,EAAE;IACV3B,QAAQ,EAAE,UAAmB;IAC7BC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRyB,MAAM,EAAE,KAAK;IACbH,UAAU,EAAE3B,QAAQ,GAChB,2DAA2D,GAC3D,2DAA2D;IAC/DgB,YAAY,EAAE,GAAG,CAAE;EACrB,CAAC;EAEDe,KAAK,EAAE;IACLR,MAAM,EAAE,CAAC;IACTS,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAElC,QAAQ,GAAG,SAAS,GAAG,SAAS;IACvCmC,aAAa,EAAE,UAAU;IACzBR,UAAU,EAAE3B,QAAQ,GAChB,mDAAmD,GACnD,mDAAmD;IACvDoC,oBAAoB,EAAE,MAAM;IAC5BC,mBAAmB,EAAE,aAAa;IAClCC,cAAc,EAAE;EAClB,CAAC;EAEDC,QAAQ,EAAE;IACRP,QAAQ,EAAE,MAAM;IAChBE,KAAK,EAAElC,QAAQ,GAAG,SAAS,GAAG,SAAS;IACvCiC,UAAU,EAAE,KAAK;IACjBO,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE;EACX,CAAC;EAEDC,WAAW,EAAE;IACXlC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBkC,GAAG,EAAE,KAAK;IACVX,QAAQ,EAAE,MAAM;IAChBE,KAAK,EAAElC,QAAQ,GAAG,SAAS,GAAG,SAAS;IACvCiC,UAAU,EAAE,KAAK;IACjBO,SAAS,EAAE,KAAK;IAChBI,aAAa,EAAE,WAAoB;IACnCT,aAAa,EAAE;EACjB,CAAC;EAEDU,KAAK,EAAE;IACLC,KAAK,EAAE,KAAK;IACZhB,MAAM,EAAE,KAAK;IACbd,YAAY,EAAE,KAAK;IACnBT,eAAe,EAAEP,QAAQ,GAAG,SAAS,GAAG,SAAS;IACjDc,SAAS,EAAE;EACb,CAAC;EAEDiC,WAAW,EAAE;IACXpB,UAAU,EAAE3B,QAAQ,GAChB,yBAAyB,GACzB,0BAA0B;IAC9BsB,MAAM,EAAE,aAAatB,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB,EAAE;IACvFgD,MAAM,EAAE,SAAS;IACjBd,KAAK,EAAElC,QAAQ,GAAG,SAAS,GAAG,SAAS;IACvCgC,QAAQ,EAAE,MAAM;IAChBd,OAAO,EAAE,MAAM;IACfF,YAAY,EAAE,MAAM;IACpBiC,UAAU,EAAE,eAAe;IAC3BzC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBoC,KAAK,EAAE,MAAM;IACbhB,MAAM,EAAE,MAAM;IACdlB,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE;EACxB,CAAC;EAEDqC,OAAO,EAAE;IACPhC,OAAO,EAAE,sBAAsB;IAAE;IACjCS,UAAU,EAAE3B,QAAQ,GAChB,mDAAmD,GACnD,mDAAmD;IACvDmD,SAAS,EAAE,MAAe;IAC1BC,IAAI,EAAE,CAAC;IACPC,SAAS,EAAE,CAAC,CAAE;EAChB,CAAC;EAEDC,cAAc,EAAE;IACdC,YAAY,EAAE,MAAM;IACpBvC,YAAY,EAAE,MAAM;IACpBwC,QAAQ,EAAE,QAAQ;IAClBlC,MAAM,EAAE,aAAatB,QAAQ,GAAG,0BAA0B,GAAG,0BAA0B,EAAE;IACzF2B,UAAU,EAAE3B,QAAQ,GAChB,uBAAuB,GACvB,0BAA0B;IAC9BY,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE;EACxB,CAAC;EAED4C,KAAK,EAAE;IACLX,KAAK,EAAE,MAAM;IACbY,cAAc,EAAE,UAAmB;IACnC1B,QAAQ,EAAE;EACZ,CAAC;EAED2B,WAAW,EAAE;IACXpD,eAAe,EAAEP,QAAQ,GACrB,yBAAyB,GACzB,0BAA0B;IAC9B0B,YAAY,EAAE,aAAa1B,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB,EAAE;IAC7FY,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE;EACxB,CAAC;EAED+C,eAAe,EAAE;IACf1C,OAAO,EAAE,WAAW;IACpB2C,SAAS,EAAE,MAAe;IAC1B5B,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAElC,QAAQ,GAAG,SAAS,GAAG,SAAS;IACvCgC,QAAQ,EAAE,MAAM;IAChBY,aAAa,EAAE,WAAoB;IACnCT,aAAa,EAAE,QAAQ;IACvBW,KAAK,EAAE,KAAK;IACZ5C,QAAQ,EAAE;EACZ,CAAC;EAED4D,SAAS,EAAE;IACTvD,eAAe,EAAE;EACnB,CAAC;EAEDwD,QAAQ,EAAE;IACRrC,YAAY,EAAE,aAAa1B,QAAQ,GAAG,0BAA0B,GAAG,0BAA0B,EAAE;IAC/FqD,SAAS,EAAE,OAAO;IAClBJ,UAAU,EAAE,eAAe;IAC3B,SAAS,EAAE;MACT1C,eAAe,EAAEP,QAAQ,GACrB,0BAA0B,GAC1B;IACN;EACF,CAAC;EAEDgE,SAAS,EAAE;IACT9C,OAAO,EAAE,MAAM;IACf+C,aAAa,EAAE,KAAc;IAC7B/B,KAAK,EAAElC,QAAQ,GAAG,SAAS,GAAG,SAAS;IACvCgC,QAAQ,EAAE,MAAM;IAChBkC,UAAU,EAAE,KAAK;IACjBpB,KAAK,EAAE;EACT,CAAC;EAEDqB,UAAU,EAAE;IACVN,SAAS,EAAE,QAAiB;IAC5B3B,KAAK,EAAElC,QAAQ,GAAG,SAAS,GAAG,SAAS;IACvCoE,SAAS,EAAE,QAAQ;IACnBpC,QAAQ,EAAE,MAAM;IAChBd,OAAO,EAAE,WAAW;IACpBS,UAAU,EAAE3B,QAAQ,GAChB,iFAAiF,GACjF;EACN,CAAC;EAEDqE,eAAe,EAAE;IACf7D,OAAO,EAAE,MAAM;IACfmC,GAAG,EAAE,MAAM;IACXzB,OAAO,EAAE,qBAAqB;IAC9BhB,QAAQ,EAAE,UAAmB;IAC7BI,MAAM,EAAE,CAAC;IACTF,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRsB,UAAU,EAAE3B,QAAQ,GAChB,iEAAiE,GACjE,oEAAoE;IACxEsE,SAAS,EAAE,aAAatE,QAAQ,GAAG,0BAA0B,GAAG,0BAA0B,EAAE;IAC5FgB,YAAY,EAAE,GAAG;IAAE;IACnBJ,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE,YAAY;IAClCI,SAAS,EAAEjB,QAAQ,GACf,gCAAgC,GAChC;EACN,CAAC;EAEDuE,YAAY,EAAE;IACZ5C,UAAU,EAAE,mDAAmD;IAC/DO,KAAK,EAAE,SAAS;IAChBZ,MAAM,EAAE,MAAM;IACdN,YAAY,EAAE,MAAM;IACpBE,OAAO,EAAE,WAAW;IACpBc,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBe,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,eAAe;IAC3BG,IAAI,EAAE,CAAC;IACP5C,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBiC,GAAG,EAAE,MAAM;IACX1B,SAAS,EAAE,oCAAoC;IAC/Cf,QAAQ,EAAE,UAAmB;IAC7BsD,QAAQ,EAAE;EACZ,CAAC;EAEDgB,YAAY,EAAE;IACZjE,eAAe,EAAE,aAAa;IAC9B2B,KAAK,EAAElC,QAAQ,GAAG,SAAS,GAAG,SAAS;IACvCsB,MAAM,EAAE,aAAatB,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB,EAAE;IACvFgB,YAAY,EAAE,MAAM;IACpBE,OAAO,EAAE,WAAW;IACpBc,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,KAAK;IACjBe,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,eAAe;IAC3BG,IAAI,EAAE,CAAC;IACPxC,cAAc,EAAE,YAAY;IAC5BC,oBAAoB,EAAE;EACxB,CAAC;EAED;EACA4D,iBAAiB,EAAE;IACjBjE,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpB8C,YAAY,EAAE,MAAM;IACpBvB,QAAQ,EAAE,MAAM;IAChBE,KAAK,EAAElC,QAAQ,GAAG,SAAS,GAAG,SAAS;IACvCkB,OAAO,EAAE,OAAO;IAChBF,YAAY,EAAE,KAAK;IACnBiC,UAAU,EAAE;EACd,CAAC;EAEDyB,QAAQ,EAAE;IACRC,WAAW,EAAE,MAAM;IACnB3B,MAAM,EAAE,SAAS;IACjBF,KAAK,EAAE,MAAM;IACbhB,MAAM,EAAE,MAAM;IACd8C,WAAW,EAAE;EACf,CAAC;EAEDC,aAAa,EAAE;IACb7B,MAAM,EAAE,SAAS;IACjBI,IAAI,EAAE,CAAC;IACPc,UAAU,EAAE;EACd,CAAC;EAED;EACA,mBAAmB,EAAE;IACnBY,IAAI,EAAE;MAAErC,OAAO,EAAE;IAAE,CAAC;IACpBsC,EAAE,EAAE;MAAEtC,OAAO,EAAE;IAAE;EACnB,CAAC;EAED,oBAAoB,EAAE;IACpBqC,IAAI,EAAE;MACJrC,OAAO,EAAE,CAAC;MACVuC,SAAS,EAAE;IACb,CAAC;IACDD,EAAE,EAAE;MACFtC,OAAO,EAAE,CAAC;MACVuC,SAAS,EAAE;IACb;EACF,CAAC;EAED,kBAAkB,EAAE;IAClB,UAAU,EAAE;MAAEvC,OAAO,EAAE;IAAE,CAAC;IAC1B,KAAK,EAAE;MAAEA,OAAO,EAAE;IAAI;EACxB,CAAC;EAED;EACAwC,iBAAiB,EAAE;IACjBtD,UAAU,EAAE,mDAAmD;IAC/DqD,SAAS,EAAE,kBAAkB;IAC7B/D,SAAS,EAAE;EACb,CAAC;EAEDiE,iBAAiB,EAAE;IACjB3E,eAAe,EAAEP,QAAQ,GACrB,yBAAyB,GACzB,0BAA0B;IAC9BmF,WAAW,EAAEnF,QAAQ,GAAG,yBAAyB,GAAG;EACtD,CAAC;EAEDoF,gBAAgB,EAAE;IAChB7E,eAAe,EAAEP,QAAQ,GACrB,yBAAyB,GACzB,yBAAyB;IAC7BgF,SAAS,EAAE;EACb;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}