{"ast": null, "code": "import { dfs } from './dfs.js';\nexport { postorder };\nfunction postorder(g, vs) {\n  return dfs(g, vs, 'post');\n}", "map": {"version": 3, "names": ["dfs", "postorder", "g", "vs"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/node_modules/dagre-d3-es/src/graphlib/alg/postorder.js"], "sourcesContent": ["import { dfs } from './dfs.js';\n\nexport { postorder };\n\nfunction postorder(g, vs) {\n  return dfs(g, vs, 'post');\n}\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,UAAU;AAE9B,SAASC,SAAS;AAElB,SAASA,SAASA,CAACC,CAAC,EAAEC,EAAE,EAAE;EACxB,OAAOH,GAAG,CAACE,CAAC,EAAEC,EAAE,EAAE,MAAM,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}