{"ast": null, "code": "// Lookahead keys are 32Bit integers in the form\n// TTTTTTTT-ZZZZZZZZZZZZ-YYYY-XXXXXXXX\n// XXXX -> Occurrence Index bitmap.\n// YYYY -> DSL Method Type bitmap.\n// ZZZZZZZZZZZZZZZ -> Rule short Index bitmap.\n// TTTTTTTTT -> alternation alternative index bitmap\nexport const BITS_FOR_METHOD_TYPE = 4;\nexport const BITS_FOR_OCCURRENCE_IDX = 8;\nexport const BITS_FOR_RULE_IDX = 12;\n// TODO: validation, this means that there may at most 2^8 --> 256 alternatives for an alternation.\nexport const BITS_FOR_ALT_IDX = 8;\n// short string used as part of mapping keys.\n// being short improves the performance when composing KEYS for maps out of these\n// The 5 - 8 bits (16 possible values, are reserved for the DSL method indices)\nexport const OR_IDX = 1 << BITS_FOR_OCCURRENCE_IDX;\nexport const OPTION_IDX = 2 << BITS_FOR_OCCURRENCE_IDX;\nexport const MANY_IDX = 3 << BITS_FOR_OCCURRENCE_IDX;\nexport const AT_LEAST_ONE_IDX = 4 << BITS_FOR_OCCURRENCE_IDX;\nexport const MANY_SEP_IDX = 5 << BITS_FOR_OCCURRENCE_IDX;\nexport const AT_LEAST_ONE_SEP_IDX = 6 << BITS_FOR_OCCURRENCE_IDX;\n// this actually returns a number, but it is always used as a string (object prop key)\nexport function getKeyForAutomaticLookahead(ruleIdx, dslMethodIdx, occurrence) {\n  return occurrence | dslMethodIdx | ruleIdx;\n}\nconst BITS_START_FOR_ALT_IDX = 32 - BITS_FOR_ALT_IDX;", "map": {"version": 3, "names": ["BITS_FOR_METHOD_TYPE", "BITS_FOR_OCCURRENCE_IDX", "BITS_FOR_RULE_IDX", "BITS_FOR_ALT_IDX", "OR_IDX", "OPTION_IDX", "MANY_IDX", "AT_LEAST_ONE_IDX", "MANY_SEP_IDX", "AT_LEAST_ONE_SEP_IDX", "getKeyForAutomaticLookahead", "ruleIdx", "dslMethodIdx", "occurrence", "BITS_START_FOR_ALT_IDX"], "sources": ["C:\\Users\\<USER>\\FixTorchUMLDGM\\node_modules\\chevrotain\\src\\parse\\grammar\\keys.ts"], "sourcesContent": ["// Lookahead keys are 32Bit integers in the form\n// TTTTTTTT-ZZZZZZZZZZZZ-YYYY-XXXXXXXX\n// XXXX -> Occurrence Index bitmap.\n// YYYY -> DSL Method Type bitmap.\n// ZZZZZZZZZZZZZZZ -> Rule short Index bitmap.\n// TTTTTTTTT -> alternation alternative index bitmap\n\nexport const BITS_FOR_METHOD_TYPE = 4;\nexport const BITS_FOR_OCCURRENCE_IDX = 8;\nexport const BITS_FOR_RULE_IDX = 12;\n// TODO: validation, this means that there may at most 2^8 --> 256 alternatives for an alternation.\nexport const BITS_FOR_ALT_IDX = 8;\n\n// short string used as part of mapping keys.\n// being short improves the performance when composing KEYS for maps out of these\n// The 5 - 8 bits (16 possible values, are reserved for the DSL method indices)\nexport const OR_IDX = 1 << BITS_FOR_OCCURRENCE_IDX;\nexport const OPTION_IDX = 2 << BITS_FOR_OCCURRENCE_IDX;\nexport const MANY_IDX = 3 << BITS_FOR_OCCURRENCE_IDX;\nexport const AT_LEAST_ONE_IDX = 4 << BITS_FOR_OCCURRENCE_IDX;\nexport const MANY_SEP_IDX = 5 << BITS_FOR_OCCURRENCE_IDX;\nexport const AT_LEAST_ONE_SEP_IDX = 6 << BITS_FOR_OCCURRENCE_IDX;\n\n// this actually returns a number, but it is always used as a string (object prop key)\nexport function getKeyForAutomaticLookahead(\n  ruleIdx: number,\n  dslMethodIdx: number,\n  occurrence: number,\n): number {\n  return occurrence | dslMethodIdx | ruleIdx;\n}\n\nconst BITS_START_FOR_ALT_IDX = 32 - BITS_FOR_ALT_IDX;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AAEA,OAAO,MAAMA,oBAAoB,GAAG,CAAC;AACrC,OAAO,MAAMC,uBAAuB,GAAG,CAAC;AACxC,OAAO,MAAMC,iBAAiB,GAAG,EAAE;AACnC;AACA,OAAO,MAAMC,gBAAgB,GAAG,CAAC;AAEjC;AACA;AACA;AACA,OAAO,MAAMC,MAAM,GAAG,CAAC,IAAIH,uBAAuB;AAClD,OAAO,MAAMI,UAAU,GAAG,CAAC,IAAIJ,uBAAuB;AACtD,OAAO,MAAMK,QAAQ,GAAG,CAAC,IAAIL,uBAAuB;AACpD,OAAO,MAAMM,gBAAgB,GAAG,CAAC,IAAIN,uBAAuB;AAC5D,OAAO,MAAMO,YAAY,GAAG,CAAC,IAAIP,uBAAuB;AACxD,OAAO,MAAMQ,oBAAoB,GAAG,CAAC,IAAIR,uBAAuB;AAEhE;AACA,OAAM,SAAUS,2BAA2BA,CACzCC,OAAe,EACfC,YAAoB,EACpBC,UAAkB;EAElB,OAAOA,UAAU,GAAGD,YAAY,GAAGD,OAAO;AAC5C;AAEA,MAAMG,sBAAsB,GAAG,EAAE,GAAGX,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}