{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\FixTorchUMLDGM\\\\uml-detector\\\\src\\\\components\\\\Documentation.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Documentation = ({\n  darkMode\n}) => {\n  _s();\n  const [activeSection, setActiveSection] = useState(\"overview\");\n  const styles = {\n    container: {\n      maxWidth: \"1200px\",\n      margin: \"0 auto\",\n      padding: \"20px\",\n      color: darkMode ? \"#e0e0e0\" : \"#333\"\n    },\n    header: {\n      textAlign: \"center\",\n      marginBottom: \"40px\",\n      borderBottom: `2px solid ${darkMode ? \"#4f46e5\" : \"#2563eb\"}`,\n      paddingBottom: \"30px\"\n    },\n    title: {\n      fontSize: \"2.5rem\",\n      fontWeight: \"700\",\n      color: darkMode ? \"#4f46e5\" : \"#2563eb\",\n      marginBottom: \"10px\"\n    },\n    subtitle: {\n      fontSize: \"1.2rem\",\n      opacity: 0.8,\n      marginBottom: \"20px\"\n    },\n    version: {\n      display: \"inline-block\",\n      backgroundColor: darkMode ? \"#4f46e5\" : \"#2563eb\",\n      color: \"white\",\n      padding: \"6px 12px\",\n      borderRadius: \"20px\",\n      fontSize: \"0.9rem\",\n      fontWeight: \"500\"\n    },\n    navigation: {\n      display: \"flex\",\n      justifyContent: \"center\",\n      flexWrap: \"wrap\",\n      gap: \"10px\",\n      marginBottom: \"40px\",\n      padding: \"20px\",\n      backgroundColor: darkMode ? \"#1e293b\" : \"#f8fafc\",\n      borderRadius: \"12px\",\n      border: `1px solid ${darkMode ? \"#334155\" : \"#e2e8f0\"}`\n    },\n    navButton: isActive => ({\n      padding: \"10px 20px\",\n      borderRadius: \"8px\",\n      border: \"none\",\n      cursor: \"pointer\",\n      fontSize: \"14px\",\n      fontWeight: \"500\",\n      transition: \"all 0.2s ease\",\n      backgroundColor: isActive ? darkMode ? \"#4f46e5\" : \"#2563eb\" : darkMode ? \"#374151\" : \"#e5e7eb\",\n      color: isActive ? \"white\" : darkMode ? \"#d1d5db\" : \"#374151\",\n      transform: isActive ? \"translateY(-2px)\" : \"none\",\n      boxShadow: isActive ? \"0 4px 12px rgba(79, 70, 229, 0.3)\" : \"none\"\n    }),\n    section: {\n      backgroundColor: darkMode ? \"#1e293b\" : \"#ffffff\",\n      borderRadius: \"12px\",\n      padding: \"30px\",\n      marginBottom: \"30px\",\n      boxShadow: darkMode ? \"0 4px 20px rgba(0,0,0,0.3)\" : \"0 4px 20px rgba(0,0,0,0.08)\",\n      border: `1px solid ${darkMode ? \"#334155\" : \"#e2e8f0\"}`\n    },\n    sectionTitle: {\n      color: darkMode ? \"#4f46e5\" : \"#2563eb\",\n      marginTop: \"0\",\n      marginBottom: \"25px\",\n      display: \"flex\",\n      alignItems: \"center\",\n      gap: \"12px\",\n      fontSize: \"1.8rem\",\n      fontWeight: \"600\"\n    },\n    featureGrid: {\n      display: \"grid\",\n      gridTemplateColumns: \"repeat(auto-fit, minmax(300px, 1fr))\",\n      gap: \"20px\",\n      marginBottom: \"30px\"\n    },\n    featureCard: {\n      backgroundColor: darkMode ? \"#2d3748\" : \"#f7fafc\",\n      borderRadius: \"10px\",\n      padding: \"20px\",\n      border: `1px solid ${darkMode ? \"#4a5568\" : \"#e2e8f0\"}`,\n      transition: \"all 0.2s ease\",\n      cursor: \"pointer\"\n    },\n    featureIcon: {\n      width: \"24px\",\n      height: \"24px\",\n      color: darkMode ? \"#4f46e5\" : \"#2563eb\",\n      marginBottom: \"15px\"\n    },\n    featureTitle: {\n      fontSize: \"1.1rem\",\n      fontWeight: \"600\",\n      marginBottom: \"10px\",\n      color: darkMode ? \"#e2e8f0\" : \"#1a202c\"\n    },\n    featureDescription: {\n      fontSize: \"0.95rem\",\n      lineHeight: \"1.6\",\n      opacity: 0.8\n    },\n    techStack: {\n      display: \"grid\",\n      gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\n      gap: \"15px\",\n      marginTop: \"20px\"\n    },\n    techItem: {\n      display: \"flex\",\n      alignItems: \"center\",\n      gap: \"10px\",\n      padding: \"12px\",\n      backgroundColor: darkMode ? \"#2d3748\" : \"#f7fafc\",\n      borderRadius: \"8px\",\n      border: `1px solid ${darkMode ? \"#4a5568\" : \"#e2e8f0\"}`\n    },\n    icon: {\n      width: \"20px\",\n      height: \"20px\",\n      flexShrink: 0\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: styles.container,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.section,\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: styles.sectionTitle,\n        children: \"Guide d'utilisation\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.featureItem,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.featureContent,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.icon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M4 16L8.586 11.414C8.961 11.039 9.47 10.828 10 10.828C10.53 10.828 11.039 11.039 11.414 11.414L16 16M14 14L15.586 12.414C15.961 12.039 16.47 11.828 17 11.828C17.53 11.828 18.039 12.039 18.414 12.414L20 14M14 8H14.01M6 20H18C19.105 20 20 19.105 20 18V6C20 4.895 19.105 4 18 4H6C4.895 4 4 4.895 4 6V18C4 19.105 4.895 20 6 20Z\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureText,\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"T\\xE9l\\xE9chargement d'image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"T\\xE9l\\xE9chargez une image contenant un diagramme de classes UML. Formats accept\\xE9s : PNG, JPG, PDF. Pour de meilleurs r\\xE9sultats, assurez-vous que le diagramme est bien lisible et que les textes sont clairs.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.featureItem,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.featureContent,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.icon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M9 3V5M15 3V5M9 19V21M15 19V21M5 9H3M5 15H3M21 9H19M21 15H19M7 19H17C18.105 19 19 18.105 19 17V7C19 5.895 18.105 5 17 5H7C5.895 5 5 5.895 5 7V17C5 18.105 5.895 19 7 19Z\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureText,\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"D\\xE9tection de relations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Notre IA identifie automatiquement les fl\\xE8ches et les lignes dans votre diagramme pour d\\xE9terminer les relations entre les classes (h\\xE9ritage, composition, agr\\xE9gation, etc.).\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.featureItem,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.featureContent,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.icon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M12 3H5C4.46957 3 3.96086 3.21071 3.58579 3.58579C3.21071 3.96086 3 4.46957 3 5V19C3 19.5304 3.21071 20.0391 3.58579 20.4142C3.96086 20.7893 4.46957 21 5 21H19C19.5304 21 20.0391 20.7893 20.4142 20.4142C20.7893 20.0391 21 19.5304 21 19V12M12 3V9M12 9H16M12 9H8M12 9V3\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: styles.featureText,\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Extraction de texte\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Extrayez automatiquement tous les textes de votre diagramme UML et organisez-les en classes, attributs et m\\xE9thodes. Les r\\xE9sultats peuvent \\xEAtre export\\xE9s en diff\\xE9rents formats.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: styles.section,\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: styles.sectionTitle,\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          style: styles.icon,\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M8.228 9C8.777 7.835 9.758 6.945 10.99 6.537C12.221 6.13 13.61 6.242 14.753 6.846C15.896 7.45 16.683 8.488 16.926 9.685C17.17 10.883 16.85 12.115 16.053 13.019C15.256 13.923 14.07 14.401 12.828 14.319C11.585 14.237 10.469 13.603 9.8 12.65M12 17V17.01M12 3C16.418 3 20 6.582 20 11C20 15.418 16.418 19 12 19C7.582 19 4 15.418 4 11C4 6.582 7.582 3 12 3Z\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), \"FAQ\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.faqItem,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.faqQuestion,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.icon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M8.228 9C8.777 7.835 9.758 6.945 10.99 6.537C12.221 6.13 13.61 6.242 14.753 6.846C15.896 7.45 16.683 8.488 16.926 9.685C17.17 10.883 16.85 12.115 16.053 13.019C15.256 13.923 14.07 14.401 12.828 14.319C11.585 14.237 10.469 13.603 9.8 12.65M12 17V17.01\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), \"Quels formats d'image sont support\\xE9s ?\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Notre application supporte les formats PNG, JPG, JPEG et PDF. Pour de meilleurs r\\xE9sultats, utilisez des images de haute r\\xE9solution et bien \\xE9clair\\xE9es.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.faqItem,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.faqQuestion,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.icon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M8.228 9C8.777 7.835 9.758 6.945 10.99 6.537C12.221 6.13 13.61 6.242 14.753 6.846C15.896 7.45 16.683 8.488 16.926 9.685C17.17 10.883 16.85 12.115 16.053 13.019C15.256 13.923 14.07 14.401 12.828 14.319C11.585 14.237 10.469 13.603 9.8 12.65M12 17V17.01\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), \"L'application fonctionne-t-elle hors ligne ?\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Non, notre application n\\xE9cessite une connexion Internet active car elle utilise des mod\\xE8les d'IA h\\xE9berg\\xE9s sur nos serveurs pour analyser vos diagrammes.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.faqItem,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.faqQuestion,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.icon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M8.228 9C8.777 7.835 9.758 6.945 10.99 6.537C12.221 6.13 13.61 6.242 14.753 6.846C15.896 7.45 16.683 8.488 16.926 9.685C17.17 10.883 16.85 12.115 16.053 13.019C15.256 13.923 14.07 14.401 12.828 14.319C11.585 14.237 10.469 13.603 9.8 12.65M12 17V17.01\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), \"Puis-je g\\xE9n\\xE9rer du code \\xE0 partir de mon diagramme UML ?\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Oui, notre fonctionnalit\\xE9 de g\\xE9n\\xE9ration de code vous permet de transformer vos diagrammes UML en code Java, Python ou TypeScript.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: styles.faqItem,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.faqQuestion,\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            style: styles.icon,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M8.228 9C8.777 7.835 9.758 6.945 10.99 6.537C12.221 6.13 13.61 6.242 14.753 6.846C15.896 7.45 16.683 8.488 16.926 9.685C17.17 10.883 16.85 12.115 16.053 13.019C15.256 13.923 14.07 14.401 12.828 14.319C11.585 14.237 10.469 13.603 9.8 12.65M12 17V17.01\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), \"Comment puis-je am\\xE9liorer la pr\\xE9cision de d\\xE9tection ?\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Pour de meilleurs r\\xE9sultats, assurez-vous que votre diagramme est bien contrast\\xE9, que les textes sont lisibles et que les lignes des relations sont clairement visibles.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: \"center\",\n        margin: \"30px 0\",\n        backgroundColor: darkMode ? \"rgba(59, 130, 246, 0.2)\" : \"rgba(59, 130, 246, 0.1)\",\n        borderRadius: \"12px\",\n        padding: \"30px 20px\",\n        border: darkMode ? \"1px solid rgba(99, 102, 241, 0.3)\" : \"1px solid rgba(59, 130, 246, 0.2)\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          marginBottom: \"15px\",\n          display: \"flex\",\n          alignItems: \"center\",\n          justifyContent: \"center\",\n          gap: \"10px\",\n          color: darkMode ? \"#EFF6FF\" : \"#1E40AF\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          style: {\n            ...styles.icon,\n            stroke: darkMode ? \"#EFF6FF\" : \"#1E40AF\"\n          },\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M18 8C18 4.686 15.314 2 12 2C8.686 2 6 4.686 6 8C6 11.313 8.686 14 12 14C15.314 14 18 11.313 18 8ZM12 18C7.582 18 4 15.313 4 11.999V16C4 17.105 4.895 18 6 18H18C19.105 18 20 17.105 20 16V12C20 15.313 16.418 18 12 18Z\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 5\n        }, this), \"Besoin d'aide suppl\\xE9mentaire ?\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 3\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          marginBottom: \"20px\",\n          color: darkMode ? \"#EFF6FF\" : \"#1E40AF\"\n        },\n        children: \"Si vous avez des questions ou rencontrez des probl\\xE8mes avec notre application, n'h\\xE9sitez pas \\xE0 contacter notre \\xE9quipe de support.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 3\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...styles.contactButton,\n          backgroundColor: darkMode ? \"#EFF6FF\" : \"#1E40AF\",\n          color: darkMode ? \"#1E40AF\" : \"#EFF6FF\",\n          border: darkMode ? \"1px solid #EFF6FF\" : \"1px solid #1E40AF\",\n          fontWeight: \"600\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          width: \"18\",\n          height: \"18\",\n          viewBox: \"0 0 24 24\",\n          fill: \"none\",\n          stroke: darkMode ? \"#1E40AF\" : \"#EFF6FF\",\n          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M22 6L12 13L2 6\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 5\n        }, this), \"Contacter le support\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 3\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 1\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this);\n};\n_s(Documentation, \"GNyxs251Uu8GpCXx9qyS0Cx9rqc=\");\n_c = Documentation;\nexport default Documentation;\nvar _c;\n$RefreshReg$(_c, \"Documentation\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Documentation", "darkMode", "_s", "activeSection", "setActiveSection", "styles", "container", "max<PERSON><PERSON><PERSON>", "margin", "padding", "color", "header", "textAlign", "marginBottom", "borderBottom", "paddingBottom", "title", "fontSize", "fontWeight", "subtitle", "opacity", "version", "display", "backgroundColor", "borderRadius", "navigation", "justifyContent", "flexWrap", "gap", "border", "navButton", "isActive", "cursor", "transition", "transform", "boxShadow", "section", "sectionTitle", "marginTop", "alignItems", "featureGrid", "gridTemplateColumns", "featureCard", "featureIcon", "width", "height", "featureTitle", "featureDescription", "lineHeight", "techStack", "techItem", "icon", "flexShrink", "style", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "featureItem", "featureContent", "viewBox", "fill", "stroke", "d", "strokeWidth", "strokeLinecap", "strokeLinejoin", "featureText", "faqItem", "faqQuestion", "contactButton", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/uml-detector/src/components/Documentation.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\n\r\ninterface DocumentationProps {\r\n  darkMode: boolean;\r\n}\r\n\r\nconst Documentation: React.FC<DocumentationProps> = ({ darkMode }) => {\r\n  const [activeSection, setActiveSection] = useState<string>(\"overview\");\r\n\r\n  const styles = {\r\n    container: {\r\n      maxWidth: \"1200px\",\r\n      margin: \"0 auto\",\r\n      padding: \"20px\",\r\n      color: darkMode ? \"#e0e0e0\" : \"#333\"\r\n    },\r\n    header: {\r\n      textAlign: \"center\" as const,\r\n      marginBottom: \"40px\",\r\n      borderBottom: `2px solid ${darkMode ? \"#4f46e5\" : \"#2563eb\"}`,\r\n      paddingBottom: \"30px\"\r\n    },\r\n    title: {\r\n      fontSize: \"2.5rem\",\r\n      fontWeight: \"700\",\r\n      color: darkMode ? \"#4f46e5\" : \"#2563eb\",\r\n      marginBottom: \"10px\"\r\n    },\r\n    subtitle: {\r\n      fontSize: \"1.2rem\",\r\n      opacity: 0.8,\r\n      marginBottom: \"20px\"\r\n    },\r\n    version: {\r\n      display: \"inline-block\",\r\n      backgroundColor: darkMode ? \"#4f46e5\" : \"#2563eb\",\r\n      color: \"white\",\r\n      padding: \"6px 12px\",\r\n      borderRadius: \"20px\",\r\n      fontSize: \"0.9rem\",\r\n      fontWeight: \"500\"\r\n    },\r\n    navigation: {\r\n      display: \"flex\",\r\n      justifyContent: \"center\",\r\n      flexWrap: \"wrap\" as const,\r\n      gap: \"10px\",\r\n      marginBottom: \"40px\",\r\n      padding: \"20px\",\r\n      backgroundColor: darkMode ? \"#1e293b\" : \"#f8fafc\",\r\n      borderRadius: \"12px\",\r\n      border: `1px solid ${darkMode ? \"#334155\" : \"#e2e8f0\"}`\r\n    },\r\n    navButton: (isActive: boolean) => ({\r\n      padding: \"10px 20px\",\r\n      borderRadius: \"8px\",\r\n      border: \"none\",\r\n      cursor: \"pointer\",\r\n      fontSize: \"14px\",\r\n      fontWeight: \"500\",\r\n      transition: \"all 0.2s ease\",\r\n      backgroundColor: isActive\r\n        ? (darkMode ? \"#4f46e5\" : \"#2563eb\")\r\n        : (darkMode ? \"#374151\" : \"#e5e7eb\"),\r\n      color: isActive\r\n        ? \"white\"\r\n        : (darkMode ? \"#d1d5db\" : \"#374151\"),\r\n      transform: isActive ? \"translateY(-2px)\" : \"none\",\r\n      boxShadow: isActive ? \"0 4px 12px rgba(79, 70, 229, 0.3)\" : \"none\"\r\n    }),\r\n    section: {\r\n      backgroundColor: darkMode ? \"#1e293b\" : \"#ffffff\",\r\n      borderRadius: \"12px\",\r\n      padding: \"30px\",\r\n      marginBottom: \"30px\",\r\n      boxShadow: darkMode\r\n        ? \"0 4px 20px rgba(0,0,0,0.3)\"\r\n        : \"0 4px 20px rgba(0,0,0,0.08)\",\r\n      border: `1px solid ${darkMode ? \"#334155\" : \"#e2e8f0\"}`\r\n    },\r\n    sectionTitle: {\r\n      color: darkMode ? \"#4f46e5\" : \"#2563eb\",\r\n      marginTop: \"0\",\r\n      marginBottom: \"25px\",\r\n      display: \"flex\",\r\n      alignItems: \"center\",\r\n      gap: \"12px\",\r\n      fontSize: \"1.8rem\",\r\n      fontWeight: \"600\"\r\n    },\r\n    featureGrid: {\r\n      display: \"grid\",\r\n      gridTemplateColumns: \"repeat(auto-fit, minmax(300px, 1fr))\",\r\n      gap: \"20px\",\r\n      marginBottom: \"30px\"\r\n    },\r\n    featureCard: {\r\n      backgroundColor: darkMode ? \"#2d3748\" : \"#f7fafc\",\r\n      borderRadius: \"10px\",\r\n      padding: \"20px\",\r\n      border: `1px solid ${darkMode ? \"#4a5568\" : \"#e2e8f0\"}`,\r\n      transition: \"all 0.2s ease\",\r\n      cursor: \"pointer\"\r\n    },\r\n    featureIcon: {\r\n      width: \"24px\",\r\n      height: \"24px\",\r\n      color: darkMode ? \"#4f46e5\" : \"#2563eb\",\r\n      marginBottom: \"15px\"\r\n    },\r\n    featureTitle: {\r\n      fontSize: \"1.1rem\",\r\n      fontWeight: \"600\",\r\n      marginBottom: \"10px\",\r\n      color: darkMode ? \"#e2e8f0\" : \"#1a202c\"\r\n    },\r\n    featureDescription: {\r\n      fontSize: \"0.95rem\",\r\n      lineHeight: \"1.6\",\r\n      opacity: 0.8\r\n    },\r\n    techStack: {\r\n      display: \"grid\",\r\n      gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\r\n      gap: \"15px\",\r\n      marginTop: \"20px\"\r\n    },\r\n    techItem: {\r\n      display: \"flex\",\r\n      alignItems: \"center\",\r\n      gap: \"10px\",\r\n      padding: \"12px\",\r\n      backgroundColor: darkMode ? \"#2d3748\" : \"#f7fafc\",\r\n      borderRadius: \"8px\",\r\n      border: `1px solid ${darkMode ? \"#4a5568\" : \"#e2e8f0\"}`\r\n    },\r\n    icon: {\r\n      width: \"20px\",\r\n      height: \"20px\",\r\n      flexShrink: 0\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div style={styles.container}>\r\n      {/* Section Guide d'utilisation */}\r\n      <div style={styles.section}>\r\n        <h2 style={styles.sectionTitle}>\r\n          Guide d'utilisation\r\n        </h2>\r\n        \r\n        <div style={styles.featureItem}>\r\n          <div style={styles.featureContent}>\r\n            <svg style={styles.icon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path d=\"M4 16L8.586 11.414C8.961 11.039 9.47 10.828 10 10.828C10.53 10.828 11.039 11.039 11.414 11.414L16 16M14 14L15.586 12.414C15.961 12.039 16.47 11.828 17 11.828C17.53 11.828 18.039 12.039 18.414 12.414L20 14M14 8H14.01M6 20H18C19.105 20 20 19.105 20 18V6C20 4.895 19.105 4 18 4H6C4.895 4 4 4.895 4 6V18C4 19.105 4.895 20 6 20Z\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n            </svg>\r\n            <div style={styles.featureText}>\r\n              <strong>Téléchargement d'image</strong>\r\n              <p>\r\n                Téléchargez une image contenant un diagramme de classes UML. Formats acceptés : PNG, JPG, PDF. \r\n                Pour de meilleurs résultats, assurez-vous que le diagramme est bien lisible et que les textes sont clairs.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        <div style={styles.featureItem}>       \r\n          <div style={styles.featureContent}>\r\n            <svg style={styles.icon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path d=\"M9 3V5M15 3V5M9 19V21M15 19V21M5 9H3M5 15H3M21 9H19M21 15H19M7 19H17C18.105 19 19 18.105 19 17V7C19 5.895 18.105 5 17 5H7C5.895 5 5 5.895 5 7V17C5 18.105 5.895 19 7 19Z\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n            </svg>\r\n            <div style={styles.featureText}>\r\n              <strong>Détection de relations</strong>\r\n              <p>\r\n                Notre IA identifie automatiquement les flèches et les lignes dans votre diagramme pour déterminer \r\n                les relations entre les classes (héritage, composition, agrégation, etc.).\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        <div style={styles.featureItem}>\r\n          <div style={styles.featureContent}>\r\n            <svg style={styles.icon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path d=\"M12 3H5C4.46957 3 3.96086 3.21071 3.58579 3.58579C3.21071 3.96086 3 4.46957 3 5V19C3 19.5304 3.21071 20.0391 3.58579 20.4142C3.96086 20.7893 4.46957 21 5 21H19C19.5304 21 20.0391 20.7893 20.4142 20.4142C20.7893 20.0391 21 19.5304 21 19V12M12 3V9M12 9H16M12 9H8M12 9V3\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n            </svg>\r\n            <div style={styles.featureText}>\r\n              <strong>Extraction de texte</strong>\r\n              <p>\r\n                Extrayez automatiquement tous les textes de votre diagramme UML et organisez-les en classes, \r\n                attributs et méthodes. Les résultats peuvent être exportés en différents formats.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Section FAQ */}\r\n      <div style={styles.section}>\r\n        <h2 style={styles.sectionTitle}>\r\n          <svg style={styles.icon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n            <path d=\"M8.228 9C8.777 7.835 9.758 6.945 10.99 6.537C12.221 6.13 13.61 6.242 14.753 6.846C15.896 7.45 16.683 8.488 16.926 9.685C17.17 10.883 16.85 12.115 16.053 13.019C15.256 13.923 14.07 14.401 12.828 14.319C11.585 14.237 10.469 13.603 9.8 12.65M12 17V17.01M12 3C16.418 3 20 6.582 20 11C20 15.418 16.418 19 12 19C7.582 19 4 15.418 4 11C4 6.582 7.582 3 12 3Z\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n          </svg>\r\n          FAQ\r\n        </h2>\r\n        \r\n        <div style={styles.faqItem}>\r\n          <div style={styles.faqQuestion}>\r\n            <svg style={styles.icon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path d=\"M8.228 9C8.777 7.835 9.758 6.945 10.99 6.537C12.221 6.13 13.61 6.242 14.753 6.846C15.896 7.45 16.683 8.488 16.926 9.685C17.17 10.883 16.85 12.115 16.053 13.019C15.256 13.923 14.07 14.401 12.828 14.319C11.585 14.237 10.469 13.603 9.8 12.65M12 17V17.01\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n            </svg>\r\n            Quels formats d'image sont supportés ?\r\n          </div>\r\n          <p>\r\n            Notre application supporte les formats PNG, JPG, JPEG et PDF. Pour de meilleurs résultats, \r\n            utilisez des images de haute résolution et bien éclairées.\r\n          </p>\r\n        </div>\r\n        \r\n        <div style={styles.faqItem}>\r\n          <div style={styles.faqQuestion}>\r\n            <svg style={styles.icon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path d=\"M8.228 9C8.777 7.835 9.758 6.945 10.99 6.537C12.221 6.13 13.61 6.242 14.753 6.846C15.896 7.45 16.683 8.488 16.926 9.685C17.17 10.883 16.85 12.115 16.053 13.019C15.256 13.923 14.07 14.401 12.828 14.319C11.585 14.237 10.469 13.603 9.8 12.65M12 17V17.01\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n            </svg>\r\n            L'application fonctionne-t-elle hors ligne ?\r\n          </div>\r\n          <p>\r\n            Non, notre application nécessite une connexion Internet active car elle utilise des modèles d'IA \r\n            hébergés sur nos serveurs pour analyser vos diagrammes.\r\n          </p>\r\n        </div>\r\n        \r\n        <div style={styles.faqItem}>\r\n          <div style={styles.faqQuestion}>\r\n            <svg style={styles.icon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path d=\"M8.228 9C8.777 7.835 9.758 6.945 10.99 6.537C12.221 6.13 13.61 6.242 14.753 6.846C15.896 7.45 16.683 8.488 16.926 9.685C17.17 10.883 16.85 12.115 16.053 13.019C15.256 13.923 14.07 14.401 12.828 14.319C11.585 14.237 10.469 13.603 9.8 12.65M12 17V17.01\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n            </svg>\r\n            Puis-je générer du code à partir de mon diagramme UML ?\r\n          </div>\r\n          <p>\r\n            Oui, notre fonctionnalité de génération de code vous permet de transformer vos diagrammes UML \r\n            en code Java, Python ou TypeScript.\r\n          </p>\r\n        </div>\r\n        \r\n        <div style={styles.faqItem}>\r\n          <div style={styles.faqQuestion}>\r\n            <svg style={styles.icon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path d=\"M8.228 9C8.777 7.835 9.758 6.945 10.99 6.537C12.221 6.13 13.61 6.242 14.753 6.846C15.896 7.45 16.683 8.488 16.926 9.685C17.17 10.883 16.85 12.115 16.053 13.019C15.256 13.923 14.07 14.401 12.828 14.319C11.585 14.237 10.469 13.603 9.8 12.65M12 17V17.01\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n            </svg>\r\n            Comment puis-je améliorer la précision de détection ?\r\n          </div>\r\n          <p>\r\n            Pour de meilleurs résultats, assurez-vous que votre diagramme est bien contrasté, que les textes \r\n            sont lisibles et que les lignes des relations sont clairement visibles.\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Section Contact */}\r\n<div style={{ \r\n  textAlign: \"center\", \r\n  margin: \"30px 0\",\r\n  backgroundColor: darkMode ? \"rgba(59, 130, 246, 0.2)\" : \"rgba(59, 130, 246, 0.1)\",\r\n  borderRadius: \"12px\",\r\n  padding: \"30px 20px\",\r\n  border: darkMode ? \"1px solid rgba(99, 102, 241, 0.3)\" : \"1px solid rgba(59, 130, 246, 0.2)\"\r\n}}>\r\n  <h3 style={{ \r\n    marginBottom: \"15px\", \r\n    display: \"flex\", \r\n    alignItems: \"center\", \r\n    justifyContent: \"center\", \r\n    gap: \"10px\",\r\n    color: darkMode ? \"#EFF6FF\" : \"#1E40AF\"\r\n  }}>\r\n    <svg style={{ ...styles.icon, stroke: darkMode ? \"#EFF6FF\" : \"#1E40AF\" }} viewBox=\"0 0 24 24\" fill=\"none\">\r\n      <path d=\"M18 8C18 4.686 15.314 2 12 2C8.686 2 6 4.686 6 8C6 11.313 8.686 14 12 14C15.314 14 18 11.313 18 8ZM12 18C7.582 18 4 15.313 4 11.999V16C4 17.105 4.895 18 6 18H18C19.105 18 20 17.105 20 16V12C20 15.313 16.418 18 12 18Z\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n    </svg>\r\n    Besoin d'aide supplémentaire ?\r\n  </h3>\r\n  <p style={{ \r\n    marginBottom: \"20px\",\r\n    color: darkMode ? \"#EFF6FF\" : \"#1E40AF\"\r\n  }}>\r\n    Si vous avez des questions ou rencontrez des problèmes avec notre application, \r\n    n'hésitez pas à contacter notre équipe de support.\r\n  </p>\r\n  <button style={{ \r\n    ...styles.contactButton,\r\n    backgroundColor: darkMode ? \"#EFF6FF\" : \"#1E40AF\",\r\n    color: darkMode ? \"#1E40AF\" : \"#EFF6FF\",\r\n    border: darkMode ? \"1px solid #EFF6FF\" : \"1px solid #1E40AF\",\r\n    fontWeight: \"600\"\r\n  }}>\r\n    <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\" stroke={darkMode ? \"#1E40AF\" : \"#EFF6FF\"}>\r\n      <path d=\"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n      <path d=\"M22 6L12 13L2 6\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n    </svg>\r\n    Contacter le support\r\n  </button>\r\n</div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Documentation;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMxC,MAAMC,aAA2C,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACpE,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGP,QAAQ,CAAS,UAAU,CAAC;EAEtE,MAAMQ,MAAM,GAAG;IACbC,SAAS,EAAE;MACTC,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,MAAM;MACfC,KAAK,EAAET,QAAQ,GAAG,SAAS,GAAG;IAChC,CAAC;IACDU,MAAM,EAAE;MACNC,SAAS,EAAE,QAAiB;MAC5BC,YAAY,EAAE,MAAM;MACpBC,YAAY,EAAE,aAAab,QAAQ,GAAG,SAAS,GAAG,SAAS,EAAE;MAC7Dc,aAAa,EAAE;IACjB,CAAC;IACDC,KAAK,EAAE;MACLC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,KAAK;MACjBR,KAAK,EAAET,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCY,YAAY,EAAE;IAChB,CAAC;IACDM,QAAQ,EAAE;MACRF,QAAQ,EAAE,QAAQ;MAClBG,OAAO,EAAE,GAAG;MACZP,YAAY,EAAE;IAChB,CAAC;IACDQ,OAAO,EAAE;MACPC,OAAO,EAAE,cAAc;MACvBC,eAAe,EAAEtB,QAAQ,GAAG,SAAS,GAAG,SAAS;MACjDS,KAAK,EAAE,OAAO;MACdD,OAAO,EAAE,UAAU;MACnBe,YAAY,EAAE,MAAM;MACpBP,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE;IACd,CAAC;IACDO,UAAU,EAAE;MACVH,OAAO,EAAE,MAAM;MACfI,cAAc,EAAE,QAAQ;MACxBC,QAAQ,EAAE,MAAe;MACzBC,GAAG,EAAE,MAAM;MACXf,YAAY,EAAE,MAAM;MACpBJ,OAAO,EAAE,MAAM;MACfc,eAAe,EAAEtB,QAAQ,GAAG,SAAS,GAAG,SAAS;MACjDuB,YAAY,EAAE,MAAM;MACpBK,MAAM,EAAE,aAAa5B,QAAQ,GAAG,SAAS,GAAG,SAAS;IACvD,CAAC;IACD6B,SAAS,EAAGC,QAAiB,KAAM;MACjCtB,OAAO,EAAE,WAAW;MACpBe,YAAY,EAAE,KAAK;MACnBK,MAAM,EAAE,MAAM;MACdG,MAAM,EAAE,SAAS;MACjBf,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBe,UAAU,EAAE,eAAe;MAC3BV,eAAe,EAAEQ,QAAQ,GACpB9B,QAAQ,GAAG,SAAS,GAAG,SAAS,GAChCA,QAAQ,GAAG,SAAS,GAAG,SAAU;MACtCS,KAAK,EAAEqB,QAAQ,GACX,OAAO,GACN9B,QAAQ,GAAG,SAAS,GAAG,SAAU;MACtCiC,SAAS,EAAEH,QAAQ,GAAG,kBAAkB,GAAG,MAAM;MACjDI,SAAS,EAAEJ,QAAQ,GAAG,mCAAmC,GAAG;IAC9D,CAAC,CAAC;IACFK,OAAO,EAAE;MACPb,eAAe,EAAEtB,QAAQ,GAAG,SAAS,GAAG,SAAS;MACjDuB,YAAY,EAAE,MAAM;MACpBf,OAAO,EAAE,MAAM;MACfI,YAAY,EAAE,MAAM;MACpBsB,SAAS,EAAElC,QAAQ,GACf,4BAA4B,GAC5B,6BAA6B;MACjC4B,MAAM,EAAE,aAAa5B,QAAQ,GAAG,SAAS,GAAG,SAAS;IACvD,CAAC;IACDoC,YAAY,EAAE;MACZ3B,KAAK,EAAET,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCqC,SAAS,EAAE,GAAG;MACdzB,YAAY,EAAE,MAAM;MACpBS,OAAO,EAAE,MAAM;MACfiB,UAAU,EAAE,QAAQ;MACpBX,GAAG,EAAE,MAAM;MACXX,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE;IACd,CAAC;IACDsB,WAAW,EAAE;MACXlB,OAAO,EAAE,MAAM;MACfmB,mBAAmB,EAAE,sCAAsC;MAC3Db,GAAG,EAAE,MAAM;MACXf,YAAY,EAAE;IAChB,CAAC;IACD6B,WAAW,EAAE;MACXnB,eAAe,EAAEtB,QAAQ,GAAG,SAAS,GAAG,SAAS;MACjDuB,YAAY,EAAE,MAAM;MACpBf,OAAO,EAAE,MAAM;MACfoB,MAAM,EAAE,aAAa5B,QAAQ,GAAG,SAAS,GAAG,SAAS,EAAE;MACvDgC,UAAU,EAAE,eAAe;MAC3BD,MAAM,EAAE;IACV,CAAC;IACDW,WAAW,EAAE;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdnC,KAAK,EAAET,QAAQ,GAAG,SAAS,GAAG,SAAS;MACvCY,YAAY,EAAE;IAChB,CAAC;IACDiC,YAAY,EAAE;MACZ7B,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,KAAK;MACjBL,YAAY,EAAE,MAAM;MACpBH,KAAK,EAAET,QAAQ,GAAG,SAAS,GAAG;IAChC,CAAC;IACD8C,kBAAkB,EAAE;MAClB9B,QAAQ,EAAE,SAAS;MACnB+B,UAAU,EAAE,KAAK;MACjB5B,OAAO,EAAE;IACX,CAAC;IACD6B,SAAS,EAAE;MACT3B,OAAO,EAAE,MAAM;MACfmB,mBAAmB,EAAE,sCAAsC;MAC3Db,GAAG,EAAE,MAAM;MACXU,SAAS,EAAE;IACb,CAAC;IACDY,QAAQ,EAAE;MACR5B,OAAO,EAAE,MAAM;MACfiB,UAAU,EAAE,QAAQ;MACpBX,GAAG,EAAE,MAAM;MACXnB,OAAO,EAAE,MAAM;MACfc,eAAe,EAAEtB,QAAQ,GAAG,SAAS,GAAG,SAAS;MACjDuB,YAAY,EAAE,KAAK;MACnBK,MAAM,EAAE,aAAa5B,QAAQ,GAAG,SAAS,GAAG,SAAS;IACvD,CAAC;IACDkD,IAAI,EAAE;MACJP,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdO,UAAU,EAAE;IACd;EACF,CAAC;EAED,oBACErD,OAAA;IAAKsD,KAAK,EAAEhD,MAAM,CAACC,SAAU;IAAAgD,QAAA,gBAE3BvD,OAAA;MAAKsD,KAAK,EAAEhD,MAAM,CAAC+B,OAAQ;MAAAkB,QAAA,gBACzBvD,OAAA;QAAIsD,KAAK,EAAEhD,MAAM,CAACgC,YAAa;QAAAiB,QAAA,EAAC;MAEhC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEL3D,OAAA;QAAKsD,KAAK,EAAEhD,MAAM,CAACsD,WAAY;QAAAL,QAAA,eAC7BvD,OAAA;UAAKsD,KAAK,EAAEhD,MAAM,CAACuD,cAAe;UAAAN,QAAA,gBAChCvD,OAAA;YAAKsD,KAAK,EAAEhD,MAAM,CAAC8C,IAAK;YAACU,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAT,QAAA,eAC5EvD,OAAA;cAAMiE,CAAC,EAAC,qUAAqU;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzY,CAAC,eACN3D,OAAA;YAAKsD,KAAK,EAAEhD,MAAM,CAAC+D,WAAY;YAAAd,QAAA,gBAC7BvD,OAAA;cAAAuD,QAAA,EAAQ;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvC3D,OAAA;cAAAuD,QAAA,EAAG;YAGH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN3D,OAAA;QAAKsD,KAAK,EAAEhD,MAAM,CAACsD,WAAY;QAAAL,QAAA,eAC7BvD,OAAA;UAAKsD,KAAK,EAAEhD,MAAM,CAACuD,cAAe;UAAAN,QAAA,gBAChCvD,OAAA;YAAKsD,KAAK,EAAEhD,MAAM,CAAC8C,IAAK;YAACU,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAT,QAAA,eAC5EvD,OAAA;cAAMiE,CAAC,EAAC,0KAA0K;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9O,CAAC,eACN3D,OAAA;YAAKsD,KAAK,EAAEhD,MAAM,CAAC+D,WAAY;YAAAd,QAAA,gBAC7BvD,OAAA;cAAAuD,QAAA,EAAQ;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvC3D,OAAA;cAAAuD,QAAA,EAAG;YAGH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN3D,OAAA;QAAKsD,KAAK,EAAEhD,MAAM,CAACsD,WAAY;QAAAL,QAAA,eAC7BvD,OAAA;UAAKsD,KAAK,EAAEhD,MAAM,CAACuD,cAAe;UAAAN,QAAA,gBAChCvD,OAAA;YAAKsD,KAAK,EAAEhD,MAAM,CAAC8C,IAAK;YAACU,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAT,QAAA,eAC5EvD,OAAA;cAAMiE,CAAC,EAAC,6QAA6Q;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjV,CAAC,eACN3D,OAAA;YAAKsD,KAAK,EAAEhD,MAAM,CAAC+D,WAAY;YAAAd,QAAA,gBAC7BvD,OAAA;cAAAuD,QAAA,EAAQ;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpC3D,OAAA;cAAAuD,QAAA,EAAG;YAGH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3D,OAAA;MAAKsD,KAAK,EAAEhD,MAAM,CAAC+B,OAAQ;MAAAkB,QAAA,gBACzBvD,OAAA;QAAIsD,KAAK,EAAEhD,MAAM,CAACgC,YAAa;QAAAiB,QAAA,gBAC7BvD,OAAA;UAAKsD,KAAK,EAAEhD,MAAM,CAAC8C,IAAK;UAACU,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAAAT,QAAA,eAC5EvD,OAAA;YAAMiE,CAAC,EAAC,gWAAgW;YAACC,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC;UAAO;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpa,CAAC,OAER;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEL3D,OAAA;QAAKsD,KAAK,EAAEhD,MAAM,CAACgE,OAAQ;QAAAf,QAAA,gBACzBvD,OAAA;UAAKsD,KAAK,EAAEhD,MAAM,CAACiE,WAAY;UAAAhB,QAAA,gBAC7BvD,OAAA;YAAKsD,KAAK,EAAEhD,MAAM,CAAC8C,IAAK;YAACU,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAT,QAAA,eAC5EvD,OAAA;cAAMiE,CAAC,EAAC,4PAA4P;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChU,CAAC,6CAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN3D,OAAA;UAAAuD,QAAA,EAAG;QAGH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN3D,OAAA;QAAKsD,KAAK,EAAEhD,MAAM,CAACgE,OAAQ;QAAAf,QAAA,gBACzBvD,OAAA;UAAKsD,KAAK,EAAEhD,MAAM,CAACiE,WAAY;UAAAhB,QAAA,gBAC7BvD,OAAA;YAAKsD,KAAK,EAAEhD,MAAM,CAAC8C,IAAK;YAACU,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAT,QAAA,eAC5EvD,OAAA;cAAMiE,CAAC,EAAC,4PAA4P;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChU,CAAC,gDAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN3D,OAAA;UAAAuD,QAAA,EAAG;QAGH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN3D,OAAA;QAAKsD,KAAK,EAAEhD,MAAM,CAACgE,OAAQ;QAAAf,QAAA,gBACzBvD,OAAA;UAAKsD,KAAK,EAAEhD,MAAM,CAACiE,WAAY;UAAAhB,QAAA,gBAC7BvD,OAAA;YAAKsD,KAAK,EAAEhD,MAAM,CAAC8C,IAAK;YAACU,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAT,QAAA,eAC5EvD,OAAA;cAAMiE,CAAC,EAAC,4PAA4P;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChU,CAAC,oEAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN3D,OAAA;UAAAuD,QAAA,EAAG;QAGH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN3D,OAAA;QAAKsD,KAAK,EAAEhD,MAAM,CAACgE,OAAQ;QAAAf,QAAA,gBACzBvD,OAAA;UAAKsD,KAAK,EAAEhD,MAAM,CAACiE,WAAY;UAAAhB,QAAA,gBAC7BvD,OAAA;YAAKsD,KAAK,EAAEhD,MAAM,CAAC8C,IAAK;YAACU,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAAAT,QAAA,eAC5EvD,OAAA;cAAMiE,CAAC,EAAC,4PAA4P;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChU,CAAC,kEAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN3D,OAAA;UAAAuD,QAAA,EAAG;QAGH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGZ3D,OAAA;MAAKsD,KAAK,EAAE;QACVzC,SAAS,EAAE,QAAQ;QACnBJ,MAAM,EAAE,QAAQ;QAChBe,eAAe,EAAEtB,QAAQ,GAAG,yBAAyB,GAAG,yBAAyB;QACjFuB,YAAY,EAAE,MAAM;QACpBf,OAAO,EAAE,WAAW;QACpBoB,MAAM,EAAE5B,QAAQ,GAAG,mCAAmC,GAAG;MAC3D,CAAE;MAAAqD,QAAA,gBACAvD,OAAA;QAAIsD,KAAK,EAAE;UACTxC,YAAY,EAAE,MAAM;UACpBS,OAAO,EAAE,MAAM;UACfiB,UAAU,EAAE,QAAQ;UACpBb,cAAc,EAAE,QAAQ;UACxBE,GAAG,EAAE,MAAM;UACXlB,KAAK,EAAET,QAAQ,GAAG,SAAS,GAAG;QAChC,CAAE;QAAAqD,QAAA,gBACAvD,OAAA;UAAKsD,KAAK,EAAE;YAAE,GAAGhD,MAAM,CAAC8C,IAAI;YAAEY,MAAM,EAAE9D,QAAQ,GAAG,SAAS,GAAG;UAAU,CAAE;UAAC4D,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAAAR,QAAA,eACvGvD,OAAA;YAAMiE,CAAC,EAAC,0NAA0N;YAACC,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC;UAAO;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9R,CAAC,qCAER;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL3D,OAAA;QAAGsD,KAAK,EAAE;UACRxC,YAAY,EAAE,MAAM;UACpBH,KAAK,EAAET,QAAQ,GAAG,SAAS,GAAG;QAChC,CAAE;QAAAqD,QAAA,EAAC;MAGH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ3D,OAAA;QAAQsD,KAAK,EAAE;UACb,GAAGhD,MAAM,CAACkE,aAAa;UACvBhD,eAAe,EAAEtB,QAAQ,GAAG,SAAS,GAAG,SAAS;UACjDS,KAAK,EAAET,QAAQ,GAAG,SAAS,GAAG,SAAS;UACvC4B,MAAM,EAAE5B,QAAQ,GAAG,mBAAmB,GAAG,mBAAmB;UAC5DiB,UAAU,EAAE;QACd,CAAE;QAAAoC,QAAA,gBACAvD,OAAA;UAAK6C,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACgB,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,MAAM;UAACC,MAAM,EAAE9D,QAAQ,GAAG,SAAS,GAAG,SAAU;UAAAqD,QAAA,gBACnGvD,OAAA;YAAMiE,CAAC,EAAC,6FAA6F;YAACC,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC;UAAO;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,eACpK3D,OAAA;YAAMiE,CAAC,EAAC,iBAAiB;YAACC,WAAW,EAAC,GAAG;YAACC,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC;UAAO;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC,wBAER;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEV,CAAC;AAACxD,EAAA,CA1SIF,aAA2C;AAAAwE,EAAA,GAA3CxE,aAA2C;AA4SjD,eAAeA,aAAa;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}