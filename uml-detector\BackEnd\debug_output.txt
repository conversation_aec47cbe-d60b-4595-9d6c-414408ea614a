=== DÉMARRAGE DÉTECTION ===
Exécution du modèle 1 (PT)...
Résultats modèle 1: 1 éléments traités
Modèle 1: 14 boîtes détectées
  Détection: class, confiance: 0.93
  [OK] Classe acceptée avec confiance 0.93 >= 0.25
  Détection: class, confiance: 0.92
  [OK] Classe acceptée avec confiance 0.92 >= 0.25
  Détection: class, confiance: 0.92
  [OK] Classe acceptée avec confiance 0.92 >= 0.25
  Détection: class, confiance: 0.92
  [OK] Classe acceptée avec confiance 0.92 >= 0.25
  Détection: class, confiance: 0.91
  [OK] Classe acceptée avec confiance 0.91 >= 0.25
  Détection: class, confiance: 0.91
  [OK] Classe acceptée avec confiance 0.91 >= 0.25
  Détection: arrow, confiance: 0.91
  [OK] Flèche acceptée avec confiance 0.91 >= 0.4
  Détection: arrow, confiance: 0.91
  [OK] Flèche acceptée avec confiance 0.91 >= 0.4
  Détection: class, confiance: 0.90
  [OK] Classe acceptée avec confiance 0.90 >= 0.25
  Détection: arrow, confiance: 0.89
  [OK] Flèche acceptée avec confiance 0.89 >= 0.4
  Détection: arrow, confiance: 0.88
  [OK] Flèche acceptée avec confiance 0.88 >= 0.4
  Détection: arrow, confiance: 0.75
  [OK] Flèche acceptée avec confiance 0.75 >= 0.4
  Détection: arrow, confiance: 0.74
  [OK] Flèche acceptée avec confiance 0.74 >= 0.4
  Détection: arrow, confiance: 0.71
  [OK] Flèche acceptée avec confiance 0.71 >= 0.4

Exécution du modèle 2 (ONNX)...
Passage de l'image brute (numpy array) au modèle ONNX...
Résultats modèle 2: 1 éléments traités
  Nombre de détections: 14
  Classe détectée: generalization
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: generalization
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
  Classe détectée: endpoin
Modèle 2: 14 boîtes détectées
  Box 0: coords=750,560,775,580 class_idx=2 conf=0.64
  Détection: generalization, confiance: 0.64
  Box 1: coords=295,123,316,144 class_idx=3 conf=0.60
  Détection: endpoin, confiance: 0.60
  Box 2: coords=171,385,195,408 class_idx=3 conf=0.59
  Détection: endpoin, confiance: 0.59
  Box 3: coords=750,614,776,634 class_idx=2 conf=0.58
  Détection: generalization, confiance: 0.58
  Box 4: coords=294,507,314,527 class_idx=3 conf=0.57
  Détection: endpoin, confiance: 0.57
  Box 5: coords=502,215,525,238 class_idx=3 conf=0.55
  Détection: endpoin, confiance: 0.55
  Box 6: coords=294,587,315,607 class_idx=3 conf=0.54
  Détection: endpoin, confiance: 0.54
  Box 7: coords=501,585,523,607 class_idx=3 conf=0.54
  Détection: endpoin, confiance: 0.54
  Box 8: coords=293,732,315,753 class_idx=3 conf=0.54
  Détection: endpoin, confiance: 0.54
  Box 9: coords=501,838,525,859 class_idx=3 conf=0.54
  Détection: endpoin, confiance: 0.54
  Box 10: coords=499,359,525,383 class_idx=3 conf=0.52
  Détection: endpoin, confiance: 0.52
  Box 11: coords=171,229,195,254 class_idx=3 conf=0.51
  Détection: endpoin, confiance: 0.51
  Box 12: coords=1108,743,1133,766 class_idx=3 conf=0.51
  Détection: endpoin, confiance: 0.51
  Box 13: coords=1108,400,1132,423 class_idx=3 conf=0.47
  Détection: endpoin, confiance: 0.47

Traitement des relations entre classes...

Résumé des détections:
  Modèle 1: {'class': 7, 'arrow': 7}
  Modèle 2: {'generalization': 2, 'endpoin': 12}

=== FIN DÉTECTION ===
Fichier mémoire mis à jour avec 12 classes

Mise à jour du texte et mémoire: class 1:

NOM_CLASSE: Client

ATTRIBUTS: 


nom
prenom
email
MÉTHODES:







----- RELATIONS DÉTECT...
Fichier mémoire mis à jour avec 12 classes

Mise à jour du texte et mémoire: class 1:

NOM_CLASSE: Client

ATTRIBUTS: 


uid : Integer
email : varchar
password : varchar
nom : v...
