{"ast": null, "code": "import { defaults, forEach } from \"lodash-es\";\nimport { resolveGrammar as orgResolveGrammar } from \"../resolver.js\";\nimport { validateGrammar as orgValidateGrammar } from \"../checks.js\";\nimport { defaultGrammarResolverErrorProvider, defaultGrammarValidatorErrorProvider } from \"../../errors_public.js\";\nexport function resolveGrammar(options) {\n  const actualOptions = defaults(options, {\n    errMsgProvider: defaultGrammarResolverErrorProvider\n  });\n  const topRulesTable = {};\n  forEach(options.rules, rule => {\n    topRulesTable[rule.name] = rule;\n  });\n  return orgResolveGrammar(topRulesTable, actualOptions.errMsgProvider);\n}\nexport function validateGrammar(options) {\n  options = defaults(options, {\n    errMsgProvider: defaultGrammarValidatorErrorProvider\n  });\n  return orgValidateGrammar(options.rules, options.tokenTypes, options.errMsgProvider, options.grammarName);\n}", "map": {"version": 3, "names": ["defaults", "for<PERSON>ach", "resolveGrammar", "orgResolveGrammar", "validate<PERSON>rammar", "orgValidateGrammar", "defaultGrammarResolverErrorProvider", "defaultGrammarValidatorErrorProvider", "options", "actualOptions", "err<PERSON><PERSON><PERSON><PERSON><PERSON>", "topRulesTable", "rules", "rule", "name", "tokenTypes", "grammarName"], "sources": ["C:\\Users\\<USER>\\FixTorchUMLDGM\\node_modules\\chevrotain\\src\\parse\\grammar\\gast\\gast_resolver_public.ts"], "sourcesContent": ["import { Rule } from \"@chevrotain/gast\";\nimport { defaults, forEach } from \"lodash-es\";\nimport { resolveGrammar as orgResolveGrammar } from \"../resolver.js\";\nimport { validateGrammar as orgValidateGrammar } from \"../checks.js\";\nimport {\n  defaultGrammarResolverErrorProvider,\n  defaultGrammarValidatorErrorProvider,\n} from \"../../errors_public.js\";\nimport { TokenType } from \"@chevrotain/types\";\nimport {\n  IGrammarResolverErrorMessageProvider,\n  IGrammarValidatorErrorMessageProvider,\n  IParserDefinitionError,\n} from \"../types.js\";\n\ntype ResolveGrammarOpts = {\n  rules: Rule[];\n  errMsgProvider?: IGrammarResolverErrorMessageProvider;\n};\nexport function resolveGrammar(\n  options: ResolveGrammarOpts,\n): IParserDefinitionError[] {\n  const actualOptions: Required<ResolveGrammarOpts> = defaults(options, {\n    errMsgProvider: defaultGrammarResolverErrorProvider,\n  });\n\n  const topRulesTable: { [ruleName: string]: Rule } = {};\n  forEach(options.rules, (rule) => {\n    topRulesTable[rule.name] = rule;\n  });\n  return orgResolveGrammar(topRulesTable, actualOptions.errMsgProvider);\n}\n\nexport function validateGrammar(options: {\n  rules: Rule[];\n  tokenTypes: TokenType[];\n  grammarName: string;\n  errMsgProvider: IGrammarValidatorErrorMessageProvider;\n}): IParserDefinitionError[] {\n  options = defaults(options, {\n    errMsgProvider: defaultGrammarValidatorErrorProvider,\n  });\n\n  return orgValidateGrammar(\n    options.rules,\n    options.tokenTypes,\n    options.errMsgProvider,\n    options.grammarName,\n  );\n}\n"], "mappings": "AACA,SAASA,QAAQ,EAAEC,OAAO,QAAQ,WAAW;AAC7C,SAASC,cAAc,IAAIC,iBAAiB,QAAQ,gBAAgB;AACpE,SAASC,eAAe,IAAIC,kBAAkB,QAAQ,cAAc;AACpE,SACEC,mCAAmC,EACnCC,oCAAoC,QAC/B,wBAAwB;AAY/B,OAAM,SAAUL,cAAcA,CAC5BM,OAA2B;EAE3B,MAAMC,aAAa,GAAiCT,QAAQ,CAACQ,OAAO,EAAE;IACpEE,cAAc,EAAEJ;GACjB,CAAC;EAEF,MAAMK,aAAa,GAAiC,EAAE;EACtDV,OAAO,CAACO,OAAO,CAACI,KAAK,EAAGC,IAAI,IAAI;IAC9BF,aAAa,CAACE,IAAI,CAACC,IAAI,CAAC,GAAGD,IAAI;EACjC,CAAC,CAAC;EACF,OAAOV,iBAAiB,CAACQ,aAAa,EAAEF,aAAa,CAACC,cAAc,CAAC;AACvE;AAEA,OAAM,SAAUN,eAAeA,CAACI,OAK/B;EACCA,OAAO,GAAGR,QAAQ,CAACQ,OAAO,EAAE;IAC1BE,cAAc,EAAEH;GACjB,CAAC;EAEF,OAAOF,kBAAkB,CACvBG,OAAO,CAACI,KAAK,EACbJ,OAAO,CAACO,UAAU,EAClBP,OAAO,CAACE,cAAc,EACtBF,OAAO,CAACQ,WAAW,CACpB;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}