// EntityPopupStyles.ts 
export const getEntityPopupStyles = (darkMode: boolean) => ({
  overlay: {
    position: 'fixed' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
    backdropFilter: 'blur(8px)',
    WebkitBackdropFilter: 'blur(8px)',
    animation: 'fadeIn 0.3s ease-out',
  },
  
  popup: {
    backgroundColor: darkMode ? '#0a0f1c' : '#ffffff',
    borderRadius: '0', // Coins droits pour tout le popup
    boxShadow: darkMode 
      ? '0 25px 50px -12px rgba(0, 0, 0, 0.8), 0 0 0 1px rgba(59, 130, 246, 0.1), 0 0 20px rgba(59, 130, 246, 0.05)' 
      : '0 25px 50px -12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(59, 130, 246, 0.05), 0 0 20px rgba(59, 130, 246, 0.02)',
    padding: '0',
    minWidth: '520px',
    maxWidth: '700px',
    maxHeight: '85vh',
    border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,
    margin: '20px',
    position: 'relative' as const,
    animation: 'slideIn 0.3s ease-out',
    display: 'flex',
    flexDirection: 'column' as const,
  },
  
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '24px 28px 20px',
    borderBottom: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}`,
    background: darkMode 
      ? 'linear-gradient(135deg, #0a0f1c 0%, #1e293b 50%, #0f172a 100%)' 
      : 'linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%)',
    borderRadius: '0', // Coins droits pour le header
    position: 'relative' as const,
    flexShrink: 0,
  },
  
  headerGlow: {
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    height: '2px',
    background: darkMode 
      ? 'linear-gradient(90deg, transparent, #3b82f6, transparent)' 
      : 'linear-gradient(90deg, transparent, #3b82f6, transparent)',
    borderRadius: '0', // Coins droits pour le headerGlow
  },
  
  title: {
    margin: 0,
    fontSize: '22px',
    fontWeight: '700',
    color: darkMode ? '#f8fafc' : '#0f172a',
    letterSpacing: '-0.025em',
    background: darkMode 
      ? 'linear-gradient(135deg, #f8fafc 0%, #cbd5e1 100%)' 
      : 'linear-gradient(135deg, #0f172a 0%, #374151 100%)',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
  },
  
  subtitle: {
    fontSize: '14px',
    color: darkMode ? '#94a3b8' : '#64748b',
    fontWeight: '500',
    marginTop: '6px',
    opacity: 0.9,
  },
  
  aiIndicator: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    fontSize: '12px',
    color: darkMode ? '#60a5fa' : '#3b82f6',
    fontWeight: '600',
    marginTop: '4px',
    textTransform: 'uppercase' as const,
    letterSpacing: '0.05em',
  },
  
  aiDot: {
    width: '6px',
    height: '6px',
    borderRadius: '50%',
    backgroundColor: darkMode ? '#60a5fa' : '#3b82f6',
    animation: 'pulse 2s infinite',
  },
  
  closeButton: {
    background: darkMode 
      ? 'rgba(59, 130, 246, 0.1)' 
      : 'rgba(59, 130, 246, 0.05)',
    border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,
    cursor: 'pointer',
    color: darkMode ? '#cbd5e1' : '#64748b',
    fontSize: '16px',
    padding: '10px',
    borderRadius: '10px',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '36px',
    height: '36px',
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
  },
  
  content: {
    padding: '28px 28px 120px 28px', // Plus d'espace en bas pour les boutons
    background: darkMode 
      ? 'linear-gradient(180deg, #0a0f1c 0%, #1e293b 100%)' 
      : 'linear-gradient(180deg, #ffffff 0%, #f8fafc 100%)',
    overflowY: 'auto' as const,
    flex: 1,
    minHeight: 0, // Permet au flex de fonctionner correctement
  },
  
  tableContainer: {
    marginBottom: '24px',
    borderRadius: '12px',
    overflow: 'hidden',
    border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}`,
    background: darkMode 
      ? 'rgba(30, 41, 59, 0.5)' 
      : 'rgba(248, 250, 252, 0.5)',
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
  },
  
  table: {
    width: '100%',
    borderCollapse: 'collapse' as const,
    fontSize: '14px',
  },
  
  tableHeader: {
    backgroundColor: darkMode 
      ? 'rgba(59, 130, 246, 0.1)' 
      : 'rgba(59, 130, 246, 0.05)',
    borderBottom: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
  },
  
  tableHeaderCell: {
    padding: '18px 24px',
    textAlign: 'left' as const,
    fontWeight: '600',
    color: darkMode ? '#e2e8f0' : '#374151',
    fontSize: '13px',
    textTransform: 'uppercase' as const,
    letterSpacing: '0.05em',
    width: '50%',
    position: 'relative' as const,
  },
  
  tableBody: {
    backgroundColor: 'transparent',
  },
  
  tableRow: {
    borderBottom: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.08)' : 'rgba(59, 130, 246, 0.05)'}`,
    minHeight: '200px',
    transition: 'all 0.2s ease',
    '&:hover': {
      backgroundColor: darkMode 
        ? 'rgba(59, 130, 246, 0.05)' 
        : 'rgba(59, 130, 246, 0.02)',
    },
  },
  
  tableCell: {
    padding: '24px',
    verticalAlign: 'top' as const,
    color: darkMode ? '#cbd5e1' : '#4b5563',
    fontSize: '14px',
    lineHeight: '1.7',
    width: '50%',
  },
  
  emptyState: {
    textAlign: 'center' as const,
    color: darkMode ? '#64748b' : '#9ca3af',
    fontStyle: 'italic',
    fontSize: '14px',
    padding: '48px 24px',
    background: darkMode 
      ? 'radial-gradient(circle at center, rgba(59, 130, 246, 0.05) 0%, transparent 70%)' 
      : 'radial-gradient(circle at center, rgba(59, 130, 246, 0.02) 0%, transparent 70%)',
  },
  
  buttonContainer: {
    display: 'flex',
    gap: '16px',
    padding: '20px 28px 24px 28px',
    position: 'absolute' as const,
    bottom: 0,
    left: 0,
    right: 0,
    background: darkMode 
      ? 'linear-gradient(180deg, rgba(10, 15, 28, 0.9) 0%, #0a0f1c 100%)' 
      : 'linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, #ffffff 100%)',
    borderTop: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.08)'}`,
    borderRadius: '0', // Coins droits pour le container des boutons
    backdropFilter: 'blur(20px)',
    WebkitBackdropFilter: 'blur(20px)',
    boxShadow: darkMode 
      ? '0 -4px 20px rgba(0, 0, 0, 0.3)' 
      : '0 -4px 20px rgba(0, 0, 0, 0.1)',
  },
  
  modifyButton: {
    background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
    color: '#ffffff',
    border: 'none',
    borderRadius: '12px',
    padding: '14px 28px',
    fontSize: '14px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    flex: 1,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '10px',
    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',
    position: 'relative' as const,
    overflow: 'hidden' as const,
  },
  
  cancelButton: {
    backgroundColor: 'transparent',
    color: darkMode ? '#94a3b8' : '#64748b',
    border: `1px solid ${darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}`,
    borderRadius: '12px',
    padding: '14px 28px',
    fontSize: '14px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    flex: 1,
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
  },
  
  // Styles pour les checkboxes
  checkboxContainer: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: '12px',
    fontSize: '14px',
    color: darkMode ? '#cbd5e1' : '#4b5563',
    padding: '8px 0',
    borderRadius: '8px',
    transition: 'all 0.2s ease',
  },
  
  checkbox: {
    marginRight: '12px',
    cursor: 'pointer',
    width: '18px',
    height: '18px',
    accentColor: '#3b82f6',
  },
  
  checkboxLabel: {
    cursor: 'pointer',
    flex: 1,
    lineHeight: '1.5',
  },
  
  // Animations CSS
  '@keyframes fadeIn': {
    from: { opacity: 0 },
    to: { opacity: 1 },
  },
  
  '@keyframes slideIn': {
    from: { 
      opacity: 0,
      transform: 'translateY(-20px) scale(0.95)',
    },
    to: { 
      opacity: 1,
      transform: 'translateY(0) scale(1)',
    },
  },
  
  '@keyframes pulse': {
    '0%, 100%': { opacity: 1 },
    '50%': { opacity: 0.5 },
  },
  
  // Styles pour les états hover
  modifyButtonHover: {
    background: 'linear-gradient(135deg, #2563eb 0%, #1e40af 100%)',
    transform: 'translateY(-2px)',
    boxShadow: '0 8px 20px rgba(59, 130, 246, 0.4)',
  },
  
  cancelButtonHover: {
    backgroundColor: darkMode 
      ? 'rgba(59, 130, 246, 0.1)' 
      : 'rgba(59, 130, 246, 0.05)',
    borderColor: darkMode ? 'rgba(59, 130, 246, 0.3)' : 'rgba(59, 130, 246, 0.2)',
  },
  
  closeButtonHover: {
    backgroundColor: darkMode 
      ? 'rgba(59, 130, 246, 0.2)' 
      : 'rgba(59, 130, 246, 0.1)',
    transform: 'scale(1.05)',
  },
});