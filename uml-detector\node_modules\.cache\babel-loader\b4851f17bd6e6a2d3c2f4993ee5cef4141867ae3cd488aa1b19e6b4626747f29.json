{"ast": null, "code": "import { BaseRegExpVisitor } from \"@chevrotain/regexp-to-ast\";\nimport { every, find, forEach, includes, isArray, values } from \"lodash-es\";\nimport { PRINT_ERROR, PRINT_WARNING } from \"@chevrotain/utils\";\nimport { getRegExpAst } from \"./reg_exp_parser.js\";\nimport { charCodeToOptimizedIndex, minOptimizationVal } from \"./lexer.js\";\nconst complementErrorMessage = \"Complement Sets are not supported for first char optimization\";\nexport const failedOptimizationPrefixMsg = 'Unable to use \"first char\" lexer optimizations:\\n';\nexport function getOptimizedStartCodesIndices(regExp, ensureOptimizations = false) {\n  try {\n    const ast = getRegExpAst(regExp);\n    const firstChars = firstCharOptimizedIndices(ast.value, {}, ast.flags.ignoreCase);\n    return firstChars;\n  } catch (e) {\n    /* istanbul ignore next */\n    // Testing this relies on the regexp-to-ast library having a bug... */\n    // TODO: only the else branch needs to be ignored, try to fix with newer prettier / tsc\n    if (e.message === complementErrorMessage) {\n      if (ensureOptimizations) {\n        PRINT_WARNING(`${failedOptimizationPrefixMsg}` + `\\tUnable to optimize: < ${regExp.toString()} >\\n` + \"\\tComplement Sets cannot be automatically optimized.\\n\" + \"\\tThis will disable the lexer's first char optimizations.\\n\" + \"\\tSee: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#COMPLEMENT for details.\");\n      }\n    } else {\n      let msgSuffix = \"\";\n      if (ensureOptimizations) {\n        msgSuffix = \"\\n\\tThis will disable the lexer's first char optimizations.\\n\" + \"\\tSee: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#REGEXP_PARSING for details.\";\n      }\n      PRINT_ERROR(`${failedOptimizationPrefixMsg}\\n` + `\\tFailed parsing: < ${regExp.toString()} >\\n` + `\\tUsing the @chevrotain/regexp-to-ast library\\n` + \"\\tPlease open an issue at: https://github.com/chevrotain/chevrotain/issues\" + msgSuffix);\n    }\n  }\n  return [];\n}\nexport function firstCharOptimizedIndices(ast, result, ignoreCase) {\n  switch (ast.type) {\n    case \"Disjunction\":\n      for (let i = 0; i < ast.value.length; i++) {\n        firstCharOptimizedIndices(ast.value[i], result, ignoreCase);\n      }\n      break;\n    case \"Alternative\":\n      const terms = ast.value;\n      for (let i = 0; i < terms.length; i++) {\n        const term = terms[i];\n        // skip terms that cannot effect the first char results\n        switch (term.type) {\n          case \"EndAnchor\":\n          // A group back reference cannot affect potential starting char.\n          // because if a back reference is the first production than automatically\n          // the group being referenced has had to come BEFORE so its codes have already been added\n          case \"GroupBackReference\":\n          // assertions do not affect potential starting codes\n          case \"Lookahead\":\n          case \"NegativeLookahead\":\n          case \"StartAnchor\":\n          case \"WordBoundary\":\n          case \"NonWordBoundary\":\n            continue;\n        }\n        const atom = term;\n        switch (atom.type) {\n          case \"Character\":\n            addOptimizedIdxToResult(atom.value, result, ignoreCase);\n            break;\n          case \"Set\":\n            if (atom.complement === true) {\n              throw Error(complementErrorMessage);\n            }\n            forEach(atom.value, code => {\n              if (typeof code === \"number\") {\n                addOptimizedIdxToResult(code, result, ignoreCase);\n              } else {\n                // range\n                const range = code;\n                // cannot optimize when ignoreCase is\n                if (ignoreCase === true) {\n                  for (let rangeCode = range.from; rangeCode <= range.to; rangeCode++) {\n                    addOptimizedIdxToResult(rangeCode, result, ignoreCase);\n                  }\n                }\n                // Optimization (2 orders of magnitude less work for very large ranges)\n                else {\n                  // handle unoptimized values\n                  for (let rangeCode = range.from; rangeCode <= range.to && rangeCode < minOptimizationVal; rangeCode++) {\n                    addOptimizedIdxToResult(rangeCode, result, ignoreCase);\n                  }\n                  // Less common charCode where we optimize for faster init time, by using larger \"buckets\"\n                  if (range.to >= minOptimizationVal) {\n                    const minUnOptVal = range.from >= minOptimizationVal ? range.from : minOptimizationVal;\n                    const maxUnOptVal = range.to;\n                    const minOptIdx = charCodeToOptimizedIndex(minUnOptVal);\n                    const maxOptIdx = charCodeToOptimizedIndex(maxUnOptVal);\n                    for (let currOptIdx = minOptIdx; currOptIdx <= maxOptIdx; currOptIdx++) {\n                      result[currOptIdx] = currOptIdx;\n                    }\n                  }\n                }\n              }\n            });\n            break;\n          case \"Group\":\n            firstCharOptimizedIndices(atom.value, result, ignoreCase);\n            break;\n          /* istanbul ignore next */\n          default:\n            throw Error(\"Non Exhaustive Match\");\n        }\n        // reached a mandatory production, no more **start** codes can be found on this alternative\n        const isOptionalQuantifier = atom.quantifier !== undefined && atom.quantifier.atLeast === 0;\n        if (\n        // A group may be optional due to empty contents /(?:)/\n        // or if everything inside it is optional /((a)?)/\n        atom.type === \"Group\" && isWholeOptional(atom) === false ||\n        // If this term is not a group it may only be optional if it has an optional quantifier\n        atom.type !== \"Group\" && isOptionalQuantifier === false) {\n          break;\n        }\n      }\n      break;\n    /* istanbul ignore next */\n    default:\n      throw Error(\"non exhaustive match!\");\n  }\n  // console.log(Object.keys(result).length)\n  return values(result);\n}\nfunction addOptimizedIdxToResult(code, result, ignoreCase) {\n  const optimizedCharIdx = charCodeToOptimizedIndex(code);\n  result[optimizedCharIdx] = optimizedCharIdx;\n  if (ignoreCase === true) {\n    handleIgnoreCase(code, result);\n  }\n}\nfunction handleIgnoreCase(code, result) {\n  const char = String.fromCharCode(code);\n  const upperChar = char.toUpperCase();\n  /* istanbul ignore else */\n  if (upperChar !== char) {\n    const optimizedCharIdx = charCodeToOptimizedIndex(upperChar.charCodeAt(0));\n    result[optimizedCharIdx] = optimizedCharIdx;\n  } else {\n    const lowerChar = char.toLowerCase();\n    if (lowerChar !== char) {\n      const optimizedCharIdx = charCodeToOptimizedIndex(lowerChar.charCodeAt(0));\n      result[optimizedCharIdx] = optimizedCharIdx;\n    }\n  }\n}\nfunction findCode(setNode, targetCharCodes) {\n  return find(setNode.value, codeOrRange => {\n    if (typeof codeOrRange === \"number\") {\n      return includes(targetCharCodes, codeOrRange);\n    } else {\n      // range\n      const range = codeOrRange;\n      return find(targetCharCodes, targetCode => range.from <= targetCode && targetCode <= range.to) !== undefined;\n    }\n  });\n}\nfunction isWholeOptional(ast) {\n  const quantifier = ast.quantifier;\n  if (quantifier && quantifier.atLeast === 0) {\n    return true;\n  }\n  if (!ast.value) {\n    return false;\n  }\n  return isArray(ast.value) ? every(ast.value, isWholeOptional) : isWholeOptional(ast.value);\n}\nclass CharCodeFinder extends BaseRegExpVisitor {\n  constructor(targetCharCodes) {\n    super();\n    this.targetCharCodes = targetCharCodes;\n    this.found = false;\n  }\n  visitChildren(node) {\n    // No need to keep looking...\n    if (this.found === true) {\n      return;\n    }\n    // switch lookaheads as they do not actually consume any characters thus\n    // finding a charCode at lookahead context does not mean that regexp can actually contain it in a match.\n    switch (node.type) {\n      case \"Lookahead\":\n        this.visitLookahead(node);\n        return;\n      case \"NegativeLookahead\":\n        this.visitNegativeLookahead(node);\n        return;\n    }\n    super.visitChildren(node);\n  }\n  visitCharacter(node) {\n    if (includes(this.targetCharCodes, node.value)) {\n      this.found = true;\n    }\n  }\n  visitSet(node) {\n    if (node.complement) {\n      if (findCode(node, this.targetCharCodes) === undefined) {\n        this.found = true;\n      }\n    } else {\n      if (findCode(node, this.targetCharCodes) !== undefined) {\n        this.found = true;\n      }\n    }\n  }\n}\nexport function canMatchCharCode(charCodes, pattern) {\n  if (pattern instanceof RegExp) {\n    const ast = getRegExpAst(pattern);\n    const charCodeFinder = new CharCodeFinder(charCodes);\n    charCodeFinder.visit(ast);\n    return charCodeFinder.found;\n  } else {\n    return find(pattern, char => {\n      return includes(charCodes, char.charCodeAt(0));\n    }) !== undefined;\n  }\n}", "map": {"version": 3, "names": ["BaseRegExpVisitor", "every", "find", "for<PERSON>ach", "includes", "isArray", "values", "PRINT_ERROR", "PRINT_WARNING", "getRegExpAst", "charCodeToOptimizedIndex", "minOptimizationVal", "complementErrorMessage", "failedOptimizationPrefixMsg", "getOptimizedStartCodesIndices", "regExp", "ensureOptimizations", "ast", "firstChars", "firstCharOptimizedIndices", "value", "flags", "ignoreCase", "e", "message", "toString", "msgSuffix", "result", "type", "i", "length", "terms", "term", "atom", "addOptimizedIdxToResult", "complement", "Error", "code", "range", "rangeCode", "from", "to", "minUnOptVal", "maxUnOptVal", "minOptIdx", "maxOptIdx", "currOptIdx", "isOptionalQuantifier", "quantifier", "undefined", "atLeast", "isWholeOptional", "optimizedCharIdx", "handleIgnoreCase", "char", "String", "fromCharCode", "upperChar", "toUpperCase", "charCodeAt", "lowerChar", "toLowerCase", "findCode", "setNode", "targetCharCodes", "codeOrRange", "targetCode", "Char<PERSON>ode<PERSON><PERSON>", "constructor", "found", "visit<PERSON><PERSON><PERSON><PERSON>", "node", "visitLookahead", "visitNegativeLookahead", "visitCharacter", "visitSet", "canMatchCharCode", "charCodes", "pattern", "RegExp", "char<PERSON><PERSON><PERSON><PERSON>", "visit"], "sources": ["C:\\Users\\<USER>\\FixTorchUMLDGM\\node_modules\\chevrotain\\src\\scan\\reg_exp.ts"], "sourcesContent": ["import {\n  Alternative,\n  Atom,\n  BaseRegExpVisitor,\n  Character,\n  Disjunction,\n  Group,\n  Set,\n} from \"@chevrotain/regexp-to-ast\";\nimport { every, find, forEach, includes, isArray, values } from \"lodash-es\";\nimport { PRINT_ERROR, PRINT_WARNING } from \"@chevrotain/utils\";\nimport { ASTNode, getRegExpAst } from \"./reg_exp_parser.js\";\nimport { charCodeToOptimizedIndex, minOptimizationVal } from \"./lexer.js\";\n\nconst complementErrorMessage =\n  \"Complement Sets are not supported for first char optimization\";\nexport const failedOptimizationPrefixMsg =\n  'Unable to use \"first char\" lexer optimizations:\\n';\n\nexport function getOptimizedStartCodesIndices(\n  regExp: RegExp,\n  ensureOptimizations = false,\n): number[] {\n  try {\n    const ast = getRegExpAst(regExp);\n    const firstChars = firstCharOptimizedIndices(\n      ast.value,\n      {},\n      ast.flags.ignoreCase,\n    );\n    return firstChars;\n  } catch (e) {\n    /* istanbul ignore next */\n    // Testing this relies on the regexp-to-ast library having a bug... */\n    // TODO: only the else branch needs to be ignored, try to fix with newer prettier / tsc\n    if (e.message === complementErrorMessage) {\n      if (ensureOptimizations) {\n        PRINT_WARNING(\n          `${failedOptimizationPrefixMsg}` +\n            `\\tUnable to optimize: < ${regExp.toString()} >\\n` +\n            \"\\tComplement Sets cannot be automatically optimized.\\n\" +\n            \"\\tThis will disable the lexer's first char optimizations.\\n\" +\n            \"\\tSee: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#COMPLEMENT for details.\",\n        );\n      }\n    } else {\n      let msgSuffix = \"\";\n      if (ensureOptimizations) {\n        msgSuffix =\n          \"\\n\\tThis will disable the lexer's first char optimizations.\\n\" +\n          \"\\tSee: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#REGEXP_PARSING for details.\";\n      }\n      PRINT_ERROR(\n        `${failedOptimizationPrefixMsg}\\n` +\n          `\\tFailed parsing: < ${regExp.toString()} >\\n` +\n          `\\tUsing the @chevrotain/regexp-to-ast library\\n` +\n          \"\\tPlease open an issue at: https://github.com/chevrotain/chevrotain/issues\" +\n          msgSuffix,\n      );\n    }\n  }\n\n  return [];\n}\n\nexport function firstCharOptimizedIndices(\n  ast: ASTNode,\n  result: { [charCode: number]: number },\n  ignoreCase: boolean,\n): number[] {\n  switch (ast.type) {\n    case \"Disjunction\":\n      for (let i = 0; i < ast.value.length; i++) {\n        firstCharOptimizedIndices(ast.value[i], result, ignoreCase);\n      }\n      break;\n    case \"Alternative\":\n      const terms = ast.value;\n      for (let i = 0; i < terms.length; i++) {\n        const term = terms[i];\n\n        // skip terms that cannot effect the first char results\n        switch (term.type) {\n          case \"EndAnchor\":\n          // A group back reference cannot affect potential starting char.\n          // because if a back reference is the first production than automatically\n          // the group being referenced has had to come BEFORE so its codes have already been added\n          case \"GroupBackReference\":\n          // assertions do not affect potential starting codes\n          case \"Lookahead\":\n          case \"NegativeLookahead\":\n          case \"StartAnchor\":\n          case \"WordBoundary\":\n          case \"NonWordBoundary\":\n            continue;\n        }\n\n        const atom = term;\n        switch (atom.type) {\n          case \"Character\":\n            addOptimizedIdxToResult(atom.value, result, ignoreCase);\n            break;\n          case \"Set\":\n            if (atom.complement === true) {\n              throw Error(complementErrorMessage);\n            }\n            forEach(atom.value, (code) => {\n              if (typeof code === \"number\") {\n                addOptimizedIdxToResult(code, result, ignoreCase);\n              } else {\n                // range\n                const range = code as any;\n                // cannot optimize when ignoreCase is\n                if (ignoreCase === true) {\n                  for (\n                    let rangeCode = range.from;\n                    rangeCode <= range.to;\n                    rangeCode++\n                  ) {\n                    addOptimizedIdxToResult(rangeCode, result, ignoreCase);\n                  }\n                }\n                // Optimization (2 orders of magnitude less work for very large ranges)\n                else {\n                  // handle unoptimized values\n                  for (\n                    let rangeCode = range.from;\n                    rangeCode <= range.to && rangeCode < minOptimizationVal;\n                    rangeCode++\n                  ) {\n                    addOptimizedIdxToResult(rangeCode, result, ignoreCase);\n                  }\n\n                  // Less common charCode where we optimize for faster init time, by using larger \"buckets\"\n                  if (range.to >= minOptimizationVal) {\n                    const minUnOptVal =\n                      range.from >= minOptimizationVal\n                        ? range.from\n                        : minOptimizationVal;\n                    const maxUnOptVal = range.to;\n                    const minOptIdx = charCodeToOptimizedIndex(minUnOptVal);\n                    const maxOptIdx = charCodeToOptimizedIndex(maxUnOptVal);\n\n                    for (\n                      let currOptIdx = minOptIdx;\n                      currOptIdx <= maxOptIdx;\n                      currOptIdx++\n                    ) {\n                      result[currOptIdx] = currOptIdx;\n                    }\n                  }\n                }\n              }\n            });\n            break;\n          case \"Group\":\n            firstCharOptimizedIndices(atom.value, result, ignoreCase);\n            break;\n          /* istanbul ignore next */\n          default:\n            throw Error(\"Non Exhaustive Match\");\n        }\n\n        // reached a mandatory production, no more **start** codes can be found on this alternative\n        const isOptionalQuantifier =\n          atom.quantifier !== undefined && atom.quantifier.atLeast === 0;\n        if (\n          // A group may be optional due to empty contents /(?:)/\n          // or if everything inside it is optional /((a)?)/\n          (atom.type === \"Group\" && isWholeOptional(atom) === false) ||\n          // If this term is not a group it may only be optional if it has an optional quantifier\n          (atom.type !== \"Group\" && isOptionalQuantifier === false)\n        ) {\n          break;\n        }\n      }\n      break;\n    /* istanbul ignore next */\n    default:\n      throw Error(\"non exhaustive match!\");\n  }\n\n  // console.log(Object.keys(result).length)\n  return values(result);\n}\n\nfunction addOptimizedIdxToResult(\n  code: number,\n  result: { [charCode: number]: number },\n  ignoreCase: boolean,\n) {\n  const optimizedCharIdx = charCodeToOptimizedIndex(code);\n  result[optimizedCharIdx] = optimizedCharIdx;\n\n  if (ignoreCase === true) {\n    handleIgnoreCase(code, result);\n  }\n}\n\nfunction handleIgnoreCase(\n  code: number,\n  result: { [charCode: number]: number },\n) {\n  const char = String.fromCharCode(code);\n  const upperChar = char.toUpperCase();\n  /* istanbul ignore else */\n  if (upperChar !== char) {\n    const optimizedCharIdx = charCodeToOptimizedIndex(upperChar.charCodeAt(0));\n    result[optimizedCharIdx] = optimizedCharIdx;\n  } else {\n    const lowerChar = char.toLowerCase();\n    if (lowerChar !== char) {\n      const optimizedCharIdx = charCodeToOptimizedIndex(\n        lowerChar.charCodeAt(0),\n      );\n      result[optimizedCharIdx] = optimizedCharIdx;\n    }\n  }\n}\n\nfunction findCode(setNode: Set, targetCharCodes: number[]) {\n  return find(setNode.value, (codeOrRange) => {\n    if (typeof codeOrRange === \"number\") {\n      return includes(targetCharCodes, codeOrRange);\n    } else {\n      // range\n      const range = <any>codeOrRange;\n      return (\n        find(\n          targetCharCodes,\n          (targetCode) => range.from <= targetCode && targetCode <= range.to,\n        ) !== undefined\n      );\n    }\n  });\n}\n\nfunction isWholeOptional(ast: any): boolean {\n  const quantifier = (ast as Atom).quantifier;\n  if (quantifier && quantifier.atLeast === 0) {\n    return true;\n  }\n\n  if (!ast.value) {\n    return false;\n  }\n\n  return isArray(ast.value)\n    ? every(ast.value, isWholeOptional)\n    : isWholeOptional(ast.value);\n}\n\nclass CharCodeFinder extends BaseRegExpVisitor {\n  found: boolean = false;\n\n  constructor(private targetCharCodes: number[]) {\n    super();\n  }\n\n  visitChildren(node: ASTNode) {\n    // No need to keep looking...\n    if (this.found === true) {\n      return;\n    }\n\n    // switch lookaheads as they do not actually consume any characters thus\n    // finding a charCode at lookahead context does not mean that regexp can actually contain it in a match.\n    switch (node.type) {\n      case \"Lookahead\":\n        this.visitLookahead(node);\n        return;\n      case \"NegativeLookahead\":\n        this.visitNegativeLookahead(node);\n        return;\n    }\n\n    super.visitChildren(node);\n  }\n\n  visitCharacter(node: Character) {\n    if (includes(this.targetCharCodes, node.value)) {\n      this.found = true;\n    }\n  }\n\n  visitSet(node: Set) {\n    if (node.complement) {\n      if (findCode(node, this.targetCharCodes) === undefined) {\n        this.found = true;\n      }\n    } else {\n      if (findCode(node, this.targetCharCodes) !== undefined) {\n        this.found = true;\n      }\n    }\n  }\n}\n\nexport function canMatchCharCode(\n  charCodes: number[],\n  pattern: RegExp | string,\n) {\n  if (pattern instanceof RegExp) {\n    const ast = getRegExpAst(pattern);\n    const charCodeFinder = new CharCodeFinder(charCodes);\n    charCodeFinder.visit(ast);\n    return charCodeFinder.found;\n  } else {\n    return (\n      find(<any>pattern, (char) => {\n        return includes(charCodes, (<string>char).charCodeAt(0));\n      }) !== undefined\n    );\n  }\n}\n"], "mappings": "AAAA,SAGEA,iBAAiB,QAKZ,2BAA2B;AAClC,SAASC,KAAK,EAAEC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,MAAM,QAAQ,WAAW;AAC3E,SAASC,WAAW,EAAEC,aAAa,QAAQ,mBAAmB;AAC9D,SAAkBC,YAAY,QAAQ,qBAAqB;AAC3D,SAASC,wBAAwB,EAAEC,kBAAkB,QAAQ,YAAY;AAEzE,MAAMC,sBAAsB,GAC1B,+DAA+D;AACjE,OAAO,MAAMC,2BAA2B,GACtC,mDAAmD;AAErD,OAAM,SAAUC,6BAA6BA,CAC3CC,MAAc,EACdC,mBAAmB,GAAG,KAAK;EAE3B,IAAI;IACF,MAAMC,GAAG,GAAGR,YAAY,CAACM,MAAM,CAAC;IAChC,MAAMG,UAAU,GAAGC,yBAAyB,CAC1CF,GAAG,CAACG,KAAK,EACT,EAAE,EACFH,GAAG,CAACI,KAAK,CAACC,UAAU,CACrB;IACD,OAAOJ,UAAU;GAClB,CAAC,OAAOK,CAAC,EAAE;IACV;IACA;IACA;IACA,IAAIA,CAAC,CAACC,OAAO,KAAKZ,sBAAsB,EAAE;MACxC,IAAII,mBAAmB,EAAE;QACvBR,aAAa,CACX,GAAGK,2BAA2B,EAAE,GAC9B,2BAA2BE,MAAM,CAACU,QAAQ,EAAE,MAAM,GAClD,wDAAwD,GACxD,6DAA6D,GAC7D,6FAA6F,CAChG;;KAEJ,MAAM;MACL,IAAIC,SAAS,GAAG,EAAE;MAClB,IAAIV,mBAAmB,EAAE;QACvBU,SAAS,GACP,+DAA+D,GAC/D,iGAAiG;;MAErGnB,WAAW,CACT,GAAGM,2BAA2B,IAAI,GAChC,uBAAuBE,MAAM,CAACU,QAAQ,EAAE,MAAM,GAC9C,iDAAiD,GACjD,4EAA4E,GAC5EC,SAAS,CACZ;;;EAIL,OAAO,EAAE;AACX;AAEA,OAAM,SAAUP,yBAAyBA,CACvCF,GAAY,EACZU,MAAsC,EACtCL,UAAmB;EAEnB,QAAQL,GAAG,CAACW,IAAI;IACd,KAAK,aAAa;MAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,GAAG,CAACG,KAAK,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;QACzCV,yBAAyB,CAACF,GAAG,CAACG,KAAK,CAACS,CAAC,CAAC,EAAEF,MAAM,EAAEL,UAAU,CAAC;;MAE7D;IACF,KAAK,aAAa;MAChB,MAAMS,KAAK,GAAGd,GAAG,CAACG,KAAK;MACvB,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,KAAK,CAACD,MAAM,EAAED,CAAC,EAAE,EAAE;QACrC,MAAMG,IAAI,GAAGD,KAAK,CAACF,CAAC,CAAC;QAErB;QACA,QAAQG,IAAI,CAACJ,IAAI;UACf,KAAK,WAAW;UAChB;UACA;UACA;UACA,KAAK,oBAAoB;UACzB;UACA,KAAK,WAAW;UAChB,KAAK,mBAAmB;UACxB,KAAK,aAAa;UAClB,KAAK,cAAc;UACnB,KAAK,iBAAiB;YACpB;;QAGJ,MAAMK,IAAI,GAAGD,IAAI;QACjB,QAAQC,IAAI,CAACL,IAAI;UACf,KAAK,WAAW;YACdM,uBAAuB,CAACD,IAAI,CAACb,KAAK,EAAEO,MAAM,EAAEL,UAAU,CAAC;YACvD;UACF,KAAK,KAAK;YACR,IAAIW,IAAI,CAACE,UAAU,KAAK,IAAI,EAAE;cAC5B,MAAMC,KAAK,CAACxB,sBAAsB,CAAC;;YAErCT,OAAO,CAAC8B,IAAI,CAACb,KAAK,EAAGiB,IAAI,IAAI;cAC3B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;gBAC5BH,uBAAuB,CAACG,IAAI,EAAEV,MAAM,EAAEL,UAAU,CAAC;eAClD,MAAM;gBACL;gBACA,MAAMgB,KAAK,GAAGD,IAAW;gBACzB;gBACA,IAAIf,UAAU,KAAK,IAAI,EAAE;kBACvB,KACE,IAAIiB,SAAS,GAAGD,KAAK,CAACE,IAAI,EAC1BD,SAAS,IAAID,KAAK,CAACG,EAAE,EACrBF,SAAS,EAAE,EACX;oBACAL,uBAAuB,CAACK,SAAS,EAAEZ,MAAM,EAAEL,UAAU,CAAC;;;gBAG1D;gBAAA,KACK;kBACH;kBACA,KACE,IAAIiB,SAAS,GAAGD,KAAK,CAACE,IAAI,EAC1BD,SAAS,IAAID,KAAK,CAACG,EAAE,IAAIF,SAAS,GAAG5B,kBAAkB,EACvD4B,SAAS,EAAE,EACX;oBACAL,uBAAuB,CAACK,SAAS,EAAEZ,MAAM,EAAEL,UAAU,CAAC;;kBAGxD;kBACA,IAAIgB,KAAK,CAACG,EAAE,IAAI9B,kBAAkB,EAAE;oBAClC,MAAM+B,WAAW,GACfJ,KAAK,CAACE,IAAI,IAAI7B,kBAAkB,GAC5B2B,KAAK,CAACE,IAAI,GACV7B,kBAAkB;oBACxB,MAAMgC,WAAW,GAAGL,KAAK,CAACG,EAAE;oBAC5B,MAAMG,SAAS,GAAGlC,wBAAwB,CAACgC,WAAW,CAAC;oBACvD,MAAMG,SAAS,GAAGnC,wBAAwB,CAACiC,WAAW,CAAC;oBAEvD,KACE,IAAIG,UAAU,GAAGF,SAAS,EAC1BE,UAAU,IAAID,SAAS,EACvBC,UAAU,EAAE,EACZ;sBACAnB,MAAM,CAACmB,UAAU,CAAC,GAAGA,UAAU;;;;;YAKzC,CAAC,CAAC;YACF;UACF,KAAK,OAAO;YACV3B,yBAAyB,CAACc,IAAI,CAACb,KAAK,EAAEO,MAAM,EAAEL,UAAU,CAAC;YACzD;UACF;UACA;YACE,MAAMc,KAAK,CAAC,sBAAsB,CAAC;;QAGvC;QACA,MAAMW,oBAAoB,GACxBd,IAAI,CAACe,UAAU,KAAKC,SAAS,IAAIhB,IAAI,CAACe,UAAU,CAACE,OAAO,KAAK,CAAC;QAChE;QACE;QACA;QACCjB,IAAI,CAACL,IAAI,KAAK,OAAO,IAAIuB,eAAe,CAAClB,IAAI,CAAC,KAAK,KAAK;QACzD;QACCA,IAAI,CAACL,IAAI,KAAK,OAAO,IAAImB,oBAAoB,KAAK,KAAM,EACzD;UACA;;;MAGJ;IACF;IACA;MACE,MAAMX,KAAK,CAAC,uBAAuB,CAAC;;EAGxC;EACA,OAAO9B,MAAM,CAACqB,MAAM,CAAC;AACvB;AAEA,SAASO,uBAAuBA,CAC9BG,IAAY,EACZV,MAAsC,EACtCL,UAAmB;EAEnB,MAAM8B,gBAAgB,GAAG1C,wBAAwB,CAAC2B,IAAI,CAAC;EACvDV,MAAM,CAACyB,gBAAgB,CAAC,GAAGA,gBAAgB;EAE3C,IAAI9B,UAAU,KAAK,IAAI,EAAE;IACvB+B,gBAAgB,CAAChB,IAAI,EAAEV,MAAM,CAAC;;AAElC;AAEA,SAAS0B,gBAAgBA,CACvBhB,IAAY,EACZV,MAAsC;EAEtC,MAAM2B,IAAI,GAAGC,MAAM,CAACC,YAAY,CAACnB,IAAI,CAAC;EACtC,MAAMoB,SAAS,GAAGH,IAAI,CAACI,WAAW,EAAE;EACpC;EACA,IAAID,SAAS,KAAKH,IAAI,EAAE;IACtB,MAAMF,gBAAgB,GAAG1C,wBAAwB,CAAC+C,SAAS,CAACE,UAAU,CAAC,CAAC,CAAC,CAAC;IAC1EhC,MAAM,CAACyB,gBAAgB,CAAC,GAAGA,gBAAgB;GAC5C,MAAM;IACL,MAAMQ,SAAS,GAAGN,IAAI,CAACO,WAAW,EAAE;IACpC,IAAID,SAAS,KAAKN,IAAI,EAAE;MACtB,MAAMF,gBAAgB,GAAG1C,wBAAwB,CAC/CkD,SAAS,CAACD,UAAU,CAAC,CAAC,CAAC,CACxB;MACDhC,MAAM,CAACyB,gBAAgB,CAAC,GAAGA,gBAAgB;;;AAGjD;AAEA,SAASU,QAAQA,CAACC,OAAY,EAAEC,eAAyB;EACvD,OAAO9D,IAAI,CAAC6D,OAAO,CAAC3C,KAAK,EAAG6C,WAAW,IAAI;IACzC,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;MACnC,OAAO7D,QAAQ,CAAC4D,eAAe,EAAEC,WAAW,CAAC;KAC9C,MAAM;MACL;MACA,MAAM3B,KAAK,GAAQ2B,WAAW;MAC9B,OACE/D,IAAI,CACF8D,eAAe,EACdE,UAAU,IAAK5B,KAAK,CAACE,IAAI,IAAI0B,UAAU,IAAIA,UAAU,IAAI5B,KAAK,CAACG,EAAE,CACnE,KAAKQ,SAAS;;EAGrB,CAAC,CAAC;AACJ;AAEA,SAASE,eAAeA,CAAClC,GAAQ;EAC/B,MAAM+B,UAAU,GAAI/B,GAAY,CAAC+B,UAAU;EAC3C,IAAIA,UAAU,IAAIA,UAAU,CAACE,OAAO,KAAK,CAAC,EAAE;IAC1C,OAAO,IAAI;;EAGb,IAAI,CAACjC,GAAG,CAACG,KAAK,EAAE;IACd,OAAO,KAAK;;EAGd,OAAOf,OAAO,CAACY,GAAG,CAACG,KAAK,CAAC,GACrBnB,KAAK,CAACgB,GAAG,CAACG,KAAK,EAAE+B,eAAe,CAAC,GACjCA,eAAe,CAAClC,GAAG,CAACG,KAAK,CAAC;AAChC;AAEA,MAAM+C,cAAe,SAAQnE,iBAAiB;EAG5CoE,YAAoBJ,eAAyB;IAC3C,KAAK,EAAE;IADW,KAAAA,eAAe,GAAfA,eAAe;IAFnC,KAAAK,KAAK,GAAY,KAAK;EAItB;EAEAC,aAAaA,CAACC,IAAa;IACzB;IACA,IAAI,IAAI,CAACF,KAAK,KAAK,IAAI,EAAE;MACvB;;IAGF;IACA;IACA,QAAQE,IAAI,CAAC3C,IAAI;MACf,KAAK,WAAW;QACd,IAAI,CAAC4C,cAAc,CAACD,IAAI,CAAC;QACzB;MACF,KAAK,mBAAmB;QACtB,IAAI,CAACE,sBAAsB,CAACF,IAAI,CAAC;QACjC;;IAGJ,KAAK,CAACD,aAAa,CAACC,IAAI,CAAC;EAC3B;EAEAG,cAAcA,CAACH,IAAe;IAC5B,IAAInE,QAAQ,CAAC,IAAI,CAAC4D,eAAe,EAAEO,IAAI,CAACnD,KAAK,CAAC,EAAE;MAC9C,IAAI,CAACiD,KAAK,GAAG,IAAI;;EAErB;EAEAM,QAAQA,CAACJ,IAAS;IAChB,IAAIA,IAAI,CAACpC,UAAU,EAAE;MACnB,IAAI2B,QAAQ,CAACS,IAAI,EAAE,IAAI,CAACP,eAAe,CAAC,KAAKf,SAAS,EAAE;QACtD,IAAI,CAACoB,KAAK,GAAG,IAAI;;KAEpB,MAAM;MACL,IAAIP,QAAQ,CAACS,IAAI,EAAE,IAAI,CAACP,eAAe,CAAC,KAAKf,SAAS,EAAE;QACtD,IAAI,CAACoB,KAAK,GAAG,IAAI;;;EAGvB;;AAGF,OAAM,SAAUO,gBAAgBA,CAC9BC,SAAmB,EACnBC,OAAwB;EAExB,IAAIA,OAAO,YAAYC,MAAM,EAAE;IAC7B,MAAM9D,GAAG,GAAGR,YAAY,CAACqE,OAAO,CAAC;IACjC,MAAME,cAAc,GAAG,IAAIb,cAAc,CAACU,SAAS,CAAC;IACpDG,cAAc,CAACC,KAAK,CAAChE,GAAG,CAAC;IACzB,OAAO+D,cAAc,CAACX,KAAK;GAC5B,MAAM;IACL,OACEnE,IAAI,CAAM4E,OAAO,EAAGxB,IAAI,IAAI;MAC1B,OAAOlD,QAAQ,CAACyE,SAAS,EAAWvB,IAAK,CAACK,UAAU,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC,CAAC,KAAKV,SAAS;;AAGtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}