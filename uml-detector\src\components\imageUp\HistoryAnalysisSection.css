/* Animations pour HistoryAnalysisSection */

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 200px;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Styles pour les éléments interactifs */
.history-match-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.history-action-button:hover {
  background-color: rgba(59, 130, 246, 0.1) !important;
  transform: scale(1.05);
}

.history-preview-container {
  animation: slideDown 0.3s ease;
}

.history-similarity-badge {
  animation: pulse 2s infinite;
}

/* Styles pour les indicateurs de sécurité */
.access-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.access-indicator.owner {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.access-indicator.shared {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.access-indicator.public {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.access-indicator.restricted {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Mode sombre */
.dark .access-indicator.owner {
  background-color: rgba(16, 185, 129, 0.2);
  color: #34d399;
}

.dark .access-indicator.shared {
  background-color: rgba(245, 158, 11, 0.2);
  color: #fbbf24;
}

.dark .access-indicator.public {
  background-color: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
}

.dark .access-indicator.restricted {
  background-color: rgba(239, 68, 68, 0.2);
  color: #f87171;
}

/* Responsive design */
@media (max-width: 768px) {
  .history-preview-content {
    grid-template-columns: 1fr !important;
  }
  
  .history-match-meta {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 4px !important;
  }
  
  .history-related-classes-list {
    flex-direction: column !important;
  }
}
