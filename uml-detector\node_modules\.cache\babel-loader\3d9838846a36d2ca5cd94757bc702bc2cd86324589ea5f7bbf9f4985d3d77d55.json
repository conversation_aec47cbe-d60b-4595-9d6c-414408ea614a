{"ast": null, "code": "/**\n * The base implementation of `_.slice` without an iteratee call guard.\n *\n * @private\n * @param {Array} array The array to slice.\n * @param {number} [start=0] The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns the slice of `array`.\n */\nfunction baseSlice(array, start, end) {\n  var index = -1,\n    length = array.length;\n  if (start < 0) {\n    start = -start > length ? 0 : length + start;\n  }\n  end = end > length ? length : end;\n  if (end < 0) {\n    end += length;\n  }\n  length = start > end ? 0 : end - start >>> 0;\n  start >>>= 0;\n  var result = Array(length);\n  while (++index < length) {\n    result[index] = array[index + start];\n  }\n  return result;\n}\nexport default baseSlice;", "map": {"version": 3, "names": ["baseSlice", "array", "start", "end", "index", "length", "result", "Array"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/node_modules/lodash-es/_baseSlice.js"], "sourcesContent": ["/**\n * The base implementation of `_.slice` without an iteratee call guard.\n *\n * @private\n * @param {Array} array The array to slice.\n * @param {number} [start=0] The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns the slice of `array`.\n */\nfunction baseSlice(array, start, end) {\n  var index = -1,\n      length = array.length;\n\n  if (start < 0) {\n    start = -start > length ? 0 : (length + start);\n  }\n  end = end > length ? length : end;\n  if (end < 0) {\n    end += length;\n  }\n  length = start > end ? 0 : ((end - start) >>> 0);\n  start >>>= 0;\n\n  var result = Array(length);\n  while (++index < length) {\n    result[index] = array[index + start];\n  }\n  return result;\n}\n\nexport default baseSlice;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAASA,CAACC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAE;EACpC,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGJ,KAAK,CAACI,MAAM;EAEzB,IAAIH,KAAK,GAAG,CAAC,EAAE;IACbA,KAAK,GAAG,CAACA,KAAK,GAAGG,MAAM,GAAG,CAAC,GAAIA,MAAM,GAAGH,KAAM;EAChD;EACAC,GAAG,GAAGA,GAAG,GAAGE,MAAM,GAAGA,MAAM,GAAGF,GAAG;EACjC,IAAIA,GAAG,GAAG,CAAC,EAAE;IACXA,GAAG,IAAIE,MAAM;EACf;EACAA,MAAM,GAAGH,KAAK,GAAGC,GAAG,GAAG,CAAC,GAAKA,GAAG,GAAGD,KAAK,KAAM,CAAE;EAChDA,KAAK,MAAM,CAAC;EAEZ,IAAII,MAAM,GAAGC,KAAK,CAACF,MAAM,CAAC;EAC1B,OAAO,EAAED,KAAK,GAAGC,MAAM,EAAE;IACvBC,MAAM,CAACF,KAAK,CAAC,GAAGH,KAAK,CAACG,KAAK,GAAGF,KAAK,CAAC;EACtC;EACA,OAAOI,MAAM;AACf;AAEA,eAAeN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}