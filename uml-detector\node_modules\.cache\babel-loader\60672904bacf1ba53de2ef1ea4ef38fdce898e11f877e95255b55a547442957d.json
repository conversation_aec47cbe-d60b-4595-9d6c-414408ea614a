{"ast": null, "code": "/* IMPORT */\nimport _ from '../utils/index.js';\nimport ChannelsReusable from '../channels/reusable.js';\nimport { DEC2HEX } from '../constants.js';\n/* MAIN */\nconst Hex = {\n  /* VARIABLES */\n  re: /^#((?:[a-f0-9]{2}){2,4}|[a-f0-9]{3})$/i,\n  /* API */\n  parse: color => {\n    if (color.charCodeAt(0) !== 35) return; // '#'\n    const match = color.match(Hex.re);\n    if (!match) return;\n    const hex = match[1];\n    const dec = parseInt(hex, 16);\n    const length = hex.length;\n    const hasAlpha = length % 4 === 0;\n    const isFullLength = length > 4;\n    const multiplier = isFullLength ? 1 : 17;\n    const bits = isFullLength ? 8 : 4;\n    const bitsOffset = hasAlpha ? 0 : -1;\n    const mask = isFullLength ? 255 : 15;\n    return ChannelsReusable.set({\n      r: (dec >> bits * (bitsOffset + 3) & mask) * multiplier,\n      g: (dec >> bits * (bitsOffset + 2) & mask) * multiplier,\n      b: (dec >> bits * (bitsOffset + 1) & mask) * multiplier,\n      a: hasAlpha ? (dec & mask) * multiplier / 255 : 1\n    }, color);\n  },\n  stringify: channels => {\n    const {\n      r,\n      g,\n      b,\n      a\n    } = channels;\n    if (a < 1) {\n      // #RRGGBBAA\n      return `#${DEC2HEX[Math.round(r)]}${DEC2HEX[Math.round(g)]}${DEC2HEX[Math.round(b)]}${DEC2HEX[Math.round(a * 255)]}`;\n    } else {\n      // #RRGGBB\n      return `#${DEC2HEX[Math.round(r)]}${DEC2HEX[Math.round(g)]}${DEC2HEX[Math.round(b)]}`;\n    }\n  }\n};\n/* EXPORT */\nexport default Hex;", "map": {"version": 3, "names": ["_", "ChannelsReusable", "DEC2HEX", "Hex", "re", "parse", "color", "charCodeAt", "match", "hex", "dec", "parseInt", "length", "has<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "multiplier", "bits", "bitsOffset", "mask", "set", "r", "g", "b", "a", "stringify", "channels", "Math", "round"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/node_modules/khroma/dist/color/hex.js"], "sourcesContent": ["/* IMPORT */\nimport _ from '../utils/index.js';\nimport ChannelsReusable from '../channels/reusable.js';\nimport { DEC2HEX } from '../constants.js';\n/* MAIN */\nconst Hex = {\n    /* VARIABLES */\n    re: /^#((?:[a-f0-9]{2}){2,4}|[a-f0-9]{3})$/i,\n    /* API */\n    parse: (color) => {\n        if (color.charCodeAt(0) !== 35)\n            return; // '#'\n        const match = color.match(Hex.re);\n        if (!match)\n            return;\n        const hex = match[1];\n        const dec = parseInt(hex, 16);\n        const length = hex.length;\n        const hasAlpha = length % 4 === 0;\n        const isFullLength = length > 4;\n        const multiplier = isFullLength ? 1 : 17;\n        const bits = isFullLength ? 8 : 4;\n        const bitsOffset = hasAlpha ? 0 : -1;\n        const mask = isFullLength ? 255 : 15;\n        return ChannelsReusable.set({\n            r: ((dec >> (bits * (bitsOffset + 3))) & mask) * multiplier,\n            g: ((dec >> (bits * (bitsOffset + 2))) & mask) * multiplier,\n            b: ((dec >> (bits * (bitsOffset + 1))) & mask) * multiplier,\n            a: hasAlpha ? (dec & mask) * multiplier / 255 : 1\n        }, color);\n    },\n    stringify: (channels) => {\n        const { r, g, b, a } = channels;\n        if (a < 1) { // #RRGGBBAA\n            return `#${DEC2HEX[Math.round(r)]}${DEC2HEX[Math.round(g)]}${DEC2HEX[Math.round(b)]}${DEC2HEX[Math.round(a * 255)]}`;\n        }\n        else { // #RRGGBB\n            return `#${DEC2HEX[Math.round(r)]}${DEC2HEX[Math.round(g)]}${DEC2HEX[Math.round(b)]}`;\n        }\n    }\n};\n/* EXPORT */\nexport default Hex;\n"], "mappings": "AAAA;AACA,OAAOA,CAAC,MAAM,mBAAmB;AACjC,OAAOC,gBAAgB,MAAM,yBAAyB;AACtD,SAASC,OAAO,QAAQ,iBAAiB;AACzC;AACA,MAAMC,GAAG,GAAG;EACR;EACAC,EAAE,EAAE,wCAAwC;EAC5C;EACAC,KAAK,EAAGC,KAAK,IAAK;IACd,IAAIA,KAAK,CAACC,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,EAC1B,OAAO,CAAC;IACZ,MAAMC,KAAK,GAAGF,KAAK,CAACE,KAAK,CAACL,GAAG,CAACC,EAAE,CAAC;IACjC,IAAI,CAACI,KAAK,EACN;IACJ,MAAMC,GAAG,GAAGD,KAAK,CAAC,CAAC,CAAC;IACpB,MAAME,GAAG,GAAGC,QAAQ,CAACF,GAAG,EAAE,EAAE,CAAC;IAC7B,MAAMG,MAAM,GAAGH,GAAG,CAACG,MAAM;IACzB,MAAMC,QAAQ,GAAGD,MAAM,GAAG,CAAC,KAAK,CAAC;IACjC,MAAME,YAAY,GAAGF,MAAM,GAAG,CAAC;IAC/B,MAAMG,UAAU,GAAGD,YAAY,GAAG,CAAC,GAAG,EAAE;IACxC,MAAME,IAAI,GAAGF,YAAY,GAAG,CAAC,GAAG,CAAC;IACjC,MAAMG,UAAU,GAAGJ,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;IACpC,MAAMK,IAAI,GAAGJ,YAAY,GAAG,GAAG,GAAG,EAAE;IACpC,OAAOb,gBAAgB,CAACkB,GAAG,CAAC;MACxBC,CAAC,EAAE,CAAEV,GAAG,IAAKM,IAAI,IAAIC,UAAU,GAAG,CAAC,CAAE,GAAIC,IAAI,IAAIH,UAAU;MAC3DM,CAAC,EAAE,CAAEX,GAAG,IAAKM,IAAI,IAAIC,UAAU,GAAG,CAAC,CAAE,GAAIC,IAAI,IAAIH,UAAU;MAC3DO,CAAC,EAAE,CAAEZ,GAAG,IAAKM,IAAI,IAAIC,UAAU,GAAG,CAAC,CAAE,GAAIC,IAAI,IAAIH,UAAU;MAC3DQ,CAAC,EAAEV,QAAQ,GAAG,CAACH,GAAG,GAAGQ,IAAI,IAAIH,UAAU,GAAG,GAAG,GAAG;IACpD,CAAC,EAAET,KAAK,CAAC;EACb,CAAC;EACDkB,SAAS,EAAGC,QAAQ,IAAK;IACrB,MAAM;MAAEL,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC;IAAE,CAAC,GAAGE,QAAQ;IAC/B,IAAIF,CAAC,GAAG,CAAC,EAAE;MAAE;MACT,OAAO,IAAIrB,OAAO,CAACwB,IAAI,CAACC,KAAK,CAACP,CAAC,CAAC,CAAC,GAAGlB,OAAO,CAACwB,IAAI,CAACC,KAAK,CAACN,CAAC,CAAC,CAAC,GAAGnB,OAAO,CAACwB,IAAI,CAACC,KAAK,CAACL,CAAC,CAAC,CAAC,GAAGpB,OAAO,CAACwB,IAAI,CAACC,KAAK,CAACJ,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE;IACxH,CAAC,MACI;MAAE;MACH,OAAO,IAAIrB,OAAO,CAACwB,IAAI,CAACC,KAAK,CAACP,CAAC,CAAC,CAAC,GAAGlB,OAAO,CAACwB,IAAI,CAACC,KAAK,CAACN,CAAC,CAAC,CAAC,GAAGnB,OAAO,CAACwB,IAAI,CAACC,KAAK,CAACL,CAAC,CAAC,CAAC,EAAE;IACzF;EACJ;AACJ,CAAC;AACD;AACA,eAAenB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}