{"ast": null, "code": "import count from \"../count.js\";\nimport deviation from \"../deviation.js\";\nexport default function (values, min, max) {\n  return Math.ceil((max - min) / (3.5 * deviation(values) * Math.pow(count(values), -1 / 3)));\n}", "map": {"version": 3, "names": ["count", "deviation", "values", "min", "max", "Math", "ceil", "pow"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/node_modules/d3-sankey/node_modules/d3-array/src/threshold/scott.js"], "sourcesContent": ["import count from \"../count.js\";\nimport deviation from \"../deviation.js\";\n\nexport default function(values, min, max) {\n  return Math.ceil((max - min) / (3.5 * deviation(values) * Math.pow(count(values), -1 / 3)));\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,SAAS,MAAM,iBAAiB;AAEvC,eAAe,UAASC,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACxC,OAAOC,IAAI,CAACC,IAAI,CAAC,CAACF,GAAG,GAAGD,GAAG,KAAK,GAAG,GAAGF,SAAS,CAACC,MAAM,CAAC,GAAGG,IAAI,CAACE,GAAG,CAACP,KAAK,CAACE,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}