NOM_CLASSE: Client
ATTRIBUTS:
uid : Integer
email : varchar
password : varchar
nom : varchar
prenom : varchar
telephone : Integer
adresse : varchar
MÉTHODES:
ajouterdemande()
modifierdemande()
----- RELATIONS DÉTECTÉES -----
• Aucune relation entre classes n'a été détectée sur ce diagramme.


NOM_CLASSE: client
ATTRIBUTS:
nom
prenom
adresse
telephone
code postal
MÉTHODES:
Passe commande()
passe commande ()
paie commande (cmd)


NOM_CLASSE: Commande
ATTRIBUTS:
numBonCommande: int
dateCommande: Date
dateReglement: Date
moyenPaiement: String
totalCommande: float
paiementValide: boolean
etatCde: string
statutCde: String
MÉTHODES:
calculerTotal()
validerPaiement()
annulerCommande()


NOM_CLASSE: Produit
ATTRIBUTS:
idProd: int
nomProd: String
prixProd: float
stockProd: int
descriptionProd: String
MÉTHODES:
ajouterStock()
retirerStock()
modifierPrix()


NOM_CLASSE: Admin
ATTRIBUTS:
aid : Integer
name : varchar
email : varchar
password : varchar
MÉTHODES:
gererUtilisateurs()
gererProduits()
consulterStatistiques()


NOM_CLASSE: Bank
ATTRIBUTS:
+BankId: int
+Name: string
+Location: string
MÉTHODES:


NOM_CLASSE: Teller
ATTRIBUTS:
Id: int
Name: string
MÉTHODES:
CollectMoney()
OpenAccount()
CloseAccount()
LoanRequest()
ProvideInfo()
IssueCard()


NOM_CLASSE: Loan
ATTRIBUTS:
+Id: int
+Type: string
+AccountId: int
+CustomerId: int
MÉTHODES:


NOM_CLASSE: Customer
ATTRIBUTS:
Id: int
Name: string
Address: string
PhoneNo: int
AcctNo: int
MÉTHODES:
+GeneralInquiry()
+DepositMoney()
+WithdrawMoney()
+OpenAccount()
+CloseAccount()
+ApplyForLoan()
+RequestCard()


NOM_CLASSE: Account
ATTRIBUTS:
Id: int
CustomerId: int
MÉTHODES:


NOM_CLASSE: Savings
ATTRIBUTS:
+Id: int
+CustomerId: int
MÉTHODES:


NOM_CLASSE: Checking
ATTRIBUTS:
+Id: int
+CustomerId: int
MÉTHODES:


