{"ast": null, "code": "import { NextAfterTokenWalker, nextPossibleTokensAfter } from \"../../grammar/interpreter.js\";\nimport { first, isUndefined } from \"lodash-es\";\nexport class ContentAssist {\n  initContentAssist() {}\n  computeContentAssist(startRuleName, precedingInput) {\n    const startRuleGast = this.gastProductionsCache[startRuleName];\n    if (isUndefined(startRuleGast)) {\n      throw Error(`Rule ->${startRuleName}<- does not exist in this grammar.`);\n    }\n    return nextPossibleTokensAfter([startRuleGast], precedingInput, this.tokenMatcher, this.maxLookahead);\n  }\n  // TODO: should this be a member method or a utility? it does not have any state or usage of 'this'...\n  // TODO: should this be more explicitly part of the public API?\n  getNextPossibleTokenTypes(grammarPath) {\n    const topRuleName = first(grammarPath.ruleStack);\n    const gastProductions = this.getGAstProductions();\n    const topProduction = gastProductions[topRuleName];\n    const nextPossibleTokenTypes = new NextAfterTokenWalker(topProduction, grammarPath).startWalking();\n    return nextPossibleTokenTypes;\n  }\n}", "map": {"version": 3, "names": ["NextAfterToken<PERSON><PERSON>er", "nextPossibleTokensAfter", "first", "isUndefined", "ContentAssist", "initContentAssist", "computeContentAssist", "startRuleName", "precedingInput", "startRuleGast", "gastProductionsCache", "Error", "tokenMatcher", "max<PERSON><PERSON><PERSON><PERSON>", "getNextPossibleTokenTypes", "grammarPath", "topRuleName", "ruleStack", "gastProductions", "getGAstProductions", "topProduction", "nextPossibleTokenTypes", "startWalking"], "sources": ["C:\\Users\\<USER>\\FixTorchUMLDGM\\node_modules\\chevrotain\\src\\parse\\parser\\traits\\context_assist.ts"], "sourcesContent": ["import {\n  ISyntacticContentAssistPath,\n  IToken,\n  ITokenGrammarPath,\n  TokenType,\n} from \"@chevrotain/types\";\nimport {\n  NextAfterTokenWalker,\n  nextPossibleTokensAfter,\n} from \"../../grammar/interpreter.js\";\nimport { first, isUndefined } from \"lodash-es\";\nimport { MixedInParser } from \"./parser_traits.js\";\n\nexport class ContentAssist {\n  initContentAssist() {}\n\n  public computeContentAssist(\n    this: MixedInParser,\n    startRuleName: string,\n    precedingInput: IToken[],\n  ): ISyntacticContentAssistPath[] {\n    const startRuleGast = this.gastProductionsCache[startRuleName];\n\n    if (isUndefined(startRuleGast)) {\n      throw Error(`Rule ->${startRuleName}<- does not exist in this grammar.`);\n    }\n\n    return nextPossibleTokensAfter(\n      [startRuleGast],\n      precedingInput,\n      this.tokenMatcher,\n      this.maxLookahead,\n    );\n  }\n\n  // TODO: should this be a member method or a utility? it does not have any state or usage of 'this'...\n  // TODO: should this be more explicitly part of the public API?\n  public getNextPossibleTokenTypes(\n    this: MixedInParser,\n    grammarPath: ITokenGrammarPath,\n  ): TokenType[] {\n    const topRuleName = first(grammarPath.ruleStack)!;\n    const gastProductions = this.getGAstProductions();\n    const topProduction = gastProductions[topRuleName];\n    const nextPossibleTokenTypes = new NextAfterTokenWalker(\n      topProduction,\n      grammarPath,\n    ).startWalking();\n    return nextPossibleTokenTypes;\n  }\n}\n"], "mappings": "AAMA,SACEA,oBAAoB,EACpBC,uBAAuB,QAClB,8BAA8B;AACrC,SAASC,KAAK,EAAEC,WAAW,QAAQ,WAAW;AAG9C,OAAM,MAAOC,aAAa;EACxBC,iBAAiBA,CAAA,GAAI;EAEdC,oBAAoBA,CAEzBC,aAAqB,EACrBC,cAAwB;IAExB,MAAMC,aAAa,GAAG,IAAI,CAACC,oBAAoB,CAACH,aAAa,CAAC;IAE9D,IAAIJ,WAAW,CAACM,aAAa,CAAC,EAAE;MAC9B,MAAME,KAAK,CAAC,UAAUJ,aAAa,oCAAoC,CAAC;;IAG1E,OAAON,uBAAuB,CAC5B,CAACQ,aAAa,CAAC,EACfD,cAAc,EACd,IAAI,CAACI,YAAY,EACjB,IAAI,CAACC,YAAY,CAClB;EACH;EAEA;EACA;EACOC,yBAAyBA,CAE9BC,WAA8B;IAE9B,MAAMC,WAAW,GAAGd,KAAK,CAACa,WAAW,CAACE,SAAS,CAAE;IACjD,MAAMC,eAAe,GAAG,IAAI,CAACC,kBAAkB,EAAE;IACjD,MAAMC,aAAa,GAAGF,eAAe,CAACF,WAAW,CAAC;IAClD,MAAMK,sBAAsB,GAAG,IAAIrB,oBAAoB,CACrDoB,aAAa,EACbL,WAAW,CACZ,CAACO,YAAY,EAAE;IAChB,OAAOD,sBAAsB;EAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}