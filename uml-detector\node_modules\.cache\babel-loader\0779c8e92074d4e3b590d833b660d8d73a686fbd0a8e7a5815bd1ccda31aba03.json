{"ast": null, "code": "/******************************************************************************\n * Copyright 2021 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\nimport { CancellationToken } from '../utils/cancellation.js';\nimport { findNodeForKeyword, findNodeForProperty } from '../utils/grammar-utils.js';\nimport { streamAst } from '../utils/ast-utils.js';\nimport { tokenToRange } from '../utils/cst-utils.js';\nimport { interruptAndCheck, isOperationCancelled } from '../utils/promise-utils.js';\nimport { diagnosticData } from './validation-registry.js';\nexport class DefaultDocumentValidator {\n  constructor(services) {\n    this.validationRegistry = services.validation.ValidationRegistry;\n    this.metadata = services.LanguageMetaData;\n  }\n  async validateDocument(document, options = {}, cancelToken = CancellationToken.None) {\n    const parseResult = document.parseResult;\n    const diagnostics = [];\n    await interruptAndCheck(cancelToken);\n    if (!options.categories || options.categories.includes('built-in')) {\n      this.processLexingErrors(parseResult, diagnostics, options);\n      if (options.stopAfterLexingErrors && diagnostics.some(d => {\n        var _a;\n        return ((_a = d.data) === null || _a === void 0 ? void 0 : _a.code) === DocumentValidator.LexingError;\n      })) {\n        return diagnostics;\n      }\n      this.processParsingErrors(parseResult, diagnostics, options);\n      if (options.stopAfterParsingErrors && diagnostics.some(d => {\n        var _a;\n        return ((_a = d.data) === null || _a === void 0 ? void 0 : _a.code) === DocumentValidator.ParsingError;\n      })) {\n        return diagnostics;\n      }\n      this.processLinkingErrors(document, diagnostics, options);\n      if (options.stopAfterLinkingErrors && diagnostics.some(d => {\n        var _a;\n        return ((_a = d.data) === null || _a === void 0 ? void 0 : _a.code) === DocumentValidator.LinkingError;\n      })) {\n        return diagnostics;\n      }\n    }\n    // Process custom validations\n    try {\n      diagnostics.push(...(await this.validateAst(parseResult.value, options, cancelToken)));\n    } catch (err) {\n      if (isOperationCancelled(err)) {\n        throw err;\n      }\n      console.error('An error occurred during validation:', err);\n    }\n    await interruptAndCheck(cancelToken);\n    return diagnostics;\n  }\n  processLexingErrors(parseResult, diagnostics, _options) {\n    var _a, _b, _c;\n    const lexerDiagnostics = [...parseResult.lexerErrors, ...((_b = (_a = parseResult.lexerReport) === null || _a === void 0 ? void 0 : _a.diagnostics) !== null && _b !== void 0 ? _b : [])];\n    for (const lexerDiagnostic of lexerDiagnostics) {\n      const severity = (_c = lexerDiagnostic.severity) !== null && _c !== void 0 ? _c : 'error';\n      const diagnostic = {\n        severity: toDiagnosticSeverity(severity),\n        range: {\n          start: {\n            line: lexerDiagnostic.line - 1,\n            character: lexerDiagnostic.column - 1\n          },\n          end: {\n            line: lexerDiagnostic.line - 1,\n            character: lexerDiagnostic.column + lexerDiagnostic.length - 1\n          }\n        },\n        message: lexerDiagnostic.message,\n        data: toDiagnosticData(severity),\n        source: this.getSource()\n      };\n      diagnostics.push(diagnostic);\n    }\n  }\n  processParsingErrors(parseResult, diagnostics, _options) {\n    for (const parserError of parseResult.parserErrors) {\n      let range = undefined;\n      // We can run into the chevrotain error recovery here\n      // The token contained in the parser error might be automatically inserted\n      // In this case every position value will be `NaN`\n      if (isNaN(parserError.token.startOffset)) {\n        // Some special parser error types contain a `previousToken`\n        // We can simply append our diagnostic to that token\n        if ('previousToken' in parserError) {\n          const token = parserError.previousToken;\n          if (!isNaN(token.startOffset)) {\n            const position = {\n              line: token.endLine - 1,\n              character: token.endColumn\n            };\n            range = {\n              start: position,\n              end: position\n            };\n          } else {\n            // No valid prev token. Might be empty document or containing only hidden tokens.\n            // Point to document start\n            const position = {\n              line: 0,\n              character: 0\n            };\n            range = {\n              start: position,\n              end: position\n            };\n          }\n        }\n      } else {\n        range = tokenToRange(parserError.token);\n      }\n      if (range) {\n        const diagnostic = {\n          severity: toDiagnosticSeverity('error'),\n          range,\n          message: parserError.message,\n          data: diagnosticData(DocumentValidator.ParsingError),\n          source: this.getSource()\n        };\n        diagnostics.push(diagnostic);\n      }\n    }\n  }\n  processLinkingErrors(document, diagnostics, _options) {\n    for (const reference of document.references) {\n      const linkingError = reference.error;\n      if (linkingError) {\n        const info = {\n          node: linkingError.container,\n          property: linkingError.property,\n          index: linkingError.index,\n          data: {\n            code: DocumentValidator.LinkingError,\n            containerType: linkingError.container.$type,\n            property: linkingError.property,\n            refText: linkingError.reference.$refText\n          }\n        };\n        diagnostics.push(this.toDiagnostic('error', linkingError.message, info));\n      }\n    }\n  }\n  async validateAst(rootNode, options, cancelToken = CancellationToken.None) {\n    const validationItems = [];\n    const acceptor = (severity, message, info) => {\n      validationItems.push(this.toDiagnostic(severity, message, info));\n    };\n    await this.validateAstBefore(rootNode, options, acceptor, cancelToken);\n    await this.validateAstNodes(rootNode, options, acceptor, cancelToken);\n    await this.validateAstAfter(rootNode, options, acceptor, cancelToken);\n    return validationItems;\n  }\n  async validateAstBefore(rootNode, options, acceptor, cancelToken = CancellationToken.None) {\n    var _a;\n    const checksBefore = this.validationRegistry.checksBefore;\n    for (const checkBefore of checksBefore) {\n      await interruptAndCheck(cancelToken);\n      await checkBefore(rootNode, acceptor, (_a = options.categories) !== null && _a !== void 0 ? _a : [], cancelToken);\n    }\n  }\n  async validateAstNodes(rootNode, options, acceptor, cancelToken = CancellationToken.None) {\n    await Promise.all(streamAst(rootNode).map(async node => {\n      await interruptAndCheck(cancelToken);\n      const checks = this.validationRegistry.getChecks(node.$type, options.categories);\n      for (const check of checks) {\n        await check(node, acceptor, cancelToken);\n      }\n    }));\n  }\n  async validateAstAfter(rootNode, options, acceptor, cancelToken = CancellationToken.None) {\n    var _a;\n    const checksAfter = this.validationRegistry.checksAfter;\n    for (const checkAfter of checksAfter) {\n      await interruptAndCheck(cancelToken);\n      await checkAfter(rootNode, acceptor, (_a = options.categories) !== null && _a !== void 0 ? _a : [], cancelToken);\n    }\n  }\n  toDiagnostic(severity, message, info) {\n    return {\n      message,\n      range: getDiagnosticRange(info),\n      severity: toDiagnosticSeverity(severity),\n      code: info.code,\n      codeDescription: info.codeDescription,\n      tags: info.tags,\n      relatedInformation: info.relatedInformation,\n      data: info.data,\n      source: this.getSource()\n    };\n  }\n  getSource() {\n    return this.metadata.languageId;\n  }\n}\nexport function getDiagnosticRange(info) {\n  if (info.range) {\n    return info.range;\n  }\n  let cstNode;\n  if (typeof info.property === 'string') {\n    cstNode = findNodeForProperty(info.node.$cstNode, info.property, info.index);\n  } else if (typeof info.keyword === 'string') {\n    cstNode = findNodeForKeyword(info.node.$cstNode, info.keyword, info.index);\n  }\n  cstNode !== null && cstNode !== void 0 ? cstNode : cstNode = info.node.$cstNode;\n  if (!cstNode) {\n    return {\n      start: {\n        line: 0,\n        character: 0\n      },\n      end: {\n        line: 0,\n        character: 0\n      }\n    };\n  }\n  return cstNode.range;\n}\n/**\n * Transforms the diagnostic severity from the {@link LexingDiagnosticSeverity} format to LSP's `DiagnosticSeverity` format.\n *\n * @param severity The lexing diagnostic severity\n * @returns Diagnostic severity according to `vscode-languageserver-types/lib/esm/main.js#DiagnosticSeverity`\n */\nexport function toDiagnosticSeverity(severity) {\n  switch (severity) {\n    case 'error':\n      return 1;\n    case 'warning':\n      return 2;\n    case 'info':\n      return 3;\n    case 'hint':\n      return 4;\n    default:\n      throw new Error('Invalid diagnostic severity: ' + severity);\n  }\n}\nexport function toDiagnosticData(severity) {\n  switch (severity) {\n    case 'error':\n      return diagnosticData(DocumentValidator.LexingError);\n    case 'warning':\n      return diagnosticData(DocumentValidator.LexingWarning);\n    case 'info':\n      return diagnosticData(DocumentValidator.LexingInfo);\n    case 'hint':\n      return diagnosticData(DocumentValidator.LexingHint);\n    default:\n      throw new Error('Invalid diagnostic severity: ' + severity);\n  }\n}\nexport var DocumentValidator;\n(function (DocumentValidator) {\n  DocumentValidator.LexingError = 'lexing-error';\n  DocumentValidator.LexingWarning = 'lexing-warning';\n  DocumentValidator.LexingInfo = 'lexing-info';\n  DocumentValidator.LexingHint = 'lexing-hint';\n  DocumentValidator.ParsingError = 'parsing-error';\n  DocumentValidator.LinkingError = 'linking-error';\n})(DocumentValidator || (DocumentValidator = {}));", "map": {"version": 3, "names": ["CancellationToken", "findNodeForKeyword", "findNodeForProperty", "streamAst", "tokenToRange", "interruptAndCheck", "isOperationCancelled", "diagnosticData", "DefaultDocumentValidator", "constructor", "services", "validationRegistry", "validation", "ValidationRegistry", "metadata", "LanguageMetaData", "validateDocument", "document", "options", "cancelToken", "None", "parseResult", "diagnostics", "categories", "includes", "processLexingErrors", "stopAfterLexingErrors", "some", "d", "_a", "data", "code", "DocumentValidator", "LexingError", "processParsingErrors", "stopAfterParsingErrors", "ParsingE<PERSON>r", "processLinkingErrors", "stopAfterLinkingErrors", "LinkingError", "push", "validateAst", "value", "err", "console", "error", "_options", "lexerDiagnostics", "lexerErrors", "_b", "lexerReport", "lexerDiagnostic", "severity", "_c", "diagnostic", "toDiagnosticSeverity", "range", "start", "line", "character", "column", "end", "length", "message", "toDiagnosticData", "source", "getSource", "parserE<PERSON>r", "parserErrors", "undefined", "isNaN", "token", "startOffset", "previousToken", "position", "endLine", "endColumn", "reference", "references", "linkingError", "info", "node", "container", "property", "index", "containerType", "$type", "refText", "$refText", "toDiagnostic", "rootNode", "validationItems", "acceptor", "validateAstBefore", "validateAstNodes", "validateAstAfter", "checksBefore", "checkBefore", "Promise", "all", "map", "checks", "getChecks", "check", "checksAfter", "checkAfter", "getDiagnosticRange", "codeDescription", "tags", "relatedInformation", "languageId", "cstNode", "$cstNode", "keyword", "Error", "LexingWarning", "LexingInfo", "LexingHint"], "sources": ["C:\\Users\\<USER>\\FixTorchUMLDGM\\node_modules\\langium\\src\\validation\\document-validator.ts"], "sourcesContent": ["/******************************************************************************\r\n * Copyright 2021 TypeFox GmbH\r\n * This program and the accompanying materials are made available under the\r\n * terms of the MIT License, which is available in the project root.\r\n ******************************************************************************/\r\n\r\nimport type { MismatchedTokenException } from 'chevrotain';\r\nimport type { DiagnosticSeverity, Position, Range, Diagnostic } from 'vscode-languageserver-types';\r\nimport type { LanguageMetaData } from '../languages/language-meta-data.js';\r\nimport type { ParseResult } from '../parser/langium-parser.js';\r\nimport type { LangiumCoreServices } from '../services.js';\r\nimport type { AstNode, CstNode } from '../syntax-tree.js';\r\nimport type { LangiumDocument } from '../workspace/documents.js';\r\nimport type { DiagnosticData, DiagnosticInfo, ValidationAcceptor, ValidationCategory, ValidationRegistry, ValidationSeverity } from './validation-registry.js';\r\nimport { CancellationToken } from '../utils/cancellation.js';\r\nimport { findNodeForKeyword, findNodeForProperty } from '../utils/grammar-utils.js';\r\nimport { streamAst } from '../utils/ast-utils.js';\r\nimport { tokenToRange } from '../utils/cst-utils.js';\r\nimport { interruptAndCheck, isOperationCancelled } from '../utils/promise-utils.js';\r\nimport { diagnosticData } from './validation-registry.js';\r\nimport type { LexingDiagnostic, LexingDiagnosticSeverity } from '../parser/token-builder.js';\r\n\r\nexport interface ValidationOptions {\r\n    /**\r\n     * If this is set, only the checks associated with these categories are executed; otherwise\r\n     * all checks are executed. The default category if not specified to the registry is `'fast'`.\r\n     */\r\n    categories?: ValidationCategory[];\r\n    /** If true, no further diagnostics are reported if there are lexing errors. */\r\n    stopAfterLexingErrors?: boolean\r\n    /** If true, no further diagnostics are reported if there are parsing errors. */\r\n    stopAfterParsingErrors?: boolean\r\n    /** If true, no further diagnostics are reported if there are linking errors. */\r\n    stopAfterLinkingErrors?: boolean\r\n}\r\n\r\n/**\r\n * Language-specific service for validating `LangiumDocument`s.\r\n */\r\nexport interface DocumentValidator {\r\n    /**\r\n     * Validates the whole specified document.\r\n     *\r\n     * @param document specified document to validate\r\n     * @param options options to control the validation process\r\n     * @param cancelToken allows to cancel the current operation\r\n     * @throws `OperationCanceled` if a user action occurs during execution\r\n     */\r\n    validateDocument(document: LangiumDocument, options?: ValidationOptions, cancelToken?: CancellationToken): Promise<Diagnostic[]>;\r\n}\r\n\r\nexport class DefaultDocumentValidator implements DocumentValidator {\r\n\r\n    protected readonly validationRegistry: ValidationRegistry;\r\n    protected readonly metadata: LanguageMetaData;\r\n\r\n    constructor(services: LangiumCoreServices) {\r\n        this.validationRegistry = services.validation.ValidationRegistry;\r\n        this.metadata = services.LanguageMetaData;\r\n    }\r\n\r\n    async validateDocument(document: LangiumDocument, options: ValidationOptions = {}, cancelToken = CancellationToken.None): Promise<Diagnostic[]> {\r\n        const parseResult = document.parseResult;\r\n        const diagnostics: Diagnostic[] = [];\r\n\r\n        await interruptAndCheck(cancelToken);\r\n\r\n        if (!options.categories || options.categories.includes('built-in')) {\r\n            this.processLexingErrors(parseResult, diagnostics, options);\r\n            if (options.stopAfterLexingErrors && diagnostics.some(d => d.data?.code === DocumentValidator.LexingError)) {\r\n                return diagnostics;\r\n            }\r\n\r\n            this.processParsingErrors(parseResult, diagnostics, options);\r\n            if (options.stopAfterParsingErrors && diagnostics.some(d => d.data?.code === DocumentValidator.ParsingError)) {\r\n                return diagnostics;\r\n            }\r\n\r\n            this.processLinkingErrors(document, diagnostics, options);\r\n            if (options.stopAfterLinkingErrors && diagnostics.some(d => d.data?.code === DocumentValidator.LinkingError)) {\r\n                return diagnostics;\r\n            }\r\n        }\r\n\r\n        // Process custom validations\r\n        try {\r\n            diagnostics.push(...await this.validateAst(parseResult.value, options, cancelToken));\r\n        } catch (err) {\r\n            if (isOperationCancelled(err)) {\r\n                throw err;\r\n            }\r\n            console.error('An error occurred during validation:', err);\r\n        }\r\n\r\n        await interruptAndCheck(cancelToken);\r\n\r\n        return diagnostics;\r\n    }\r\n\r\n    protected processLexingErrors(parseResult: ParseResult, diagnostics: Diagnostic[], _options: ValidationOptions): void {\r\n        const lexerDiagnostics = [...parseResult.lexerErrors, ...parseResult.lexerReport?.diagnostics ?? []] as LexingDiagnostic[];\r\n        for (const lexerDiagnostic of lexerDiagnostics) {\r\n            const severity = lexerDiagnostic.severity ?? 'error';\r\n            const diagnostic: Diagnostic = {\r\n                severity: toDiagnosticSeverity(severity),\r\n                range: {\r\n                    start: {\r\n                        line: lexerDiagnostic.line! - 1,\r\n                        character: lexerDiagnostic.column! - 1\r\n                    },\r\n                    end: {\r\n                        line: lexerDiagnostic.line! - 1,\r\n                        character: lexerDiagnostic.column! + lexerDiagnostic.length - 1\r\n                    }\r\n                },\r\n                message: lexerDiagnostic.message,\r\n                data: toDiagnosticData(severity),\r\n                source: this.getSource()\r\n            };\r\n            diagnostics.push(diagnostic);\r\n        }\r\n    }\r\n\r\n    protected processParsingErrors(parseResult: ParseResult, diagnostics: Diagnostic[], _options: ValidationOptions): void {\r\n        for (const parserError of parseResult.parserErrors) {\r\n            let range: Range | undefined = undefined;\r\n            // We can run into the chevrotain error recovery here\r\n            // The token contained in the parser error might be automatically inserted\r\n            // In this case every position value will be `NaN`\r\n            if (isNaN(parserError.token.startOffset)) {\r\n                // Some special parser error types contain a `previousToken`\r\n                // We can simply append our diagnostic to that token\r\n                if ('previousToken' in parserError) {\r\n                    const token = (parserError as MismatchedTokenException).previousToken;\r\n                    if (!isNaN(token.startOffset)) {\r\n                        const position: Position = { line: token.endLine! - 1, character: token.endColumn! };\r\n                        range = { start: position, end: position};\r\n                    } else {\r\n                        // No valid prev token. Might be empty document or containing only hidden tokens.\r\n                        // Point to document start\r\n                        const position: Position = { line: 0, character: 0 };\r\n                        range = { start: position, end: position};\r\n                    }\r\n                }\r\n            } else {\r\n                range = tokenToRange(parserError.token);\r\n            }\r\n            if (range) {\r\n                const diagnostic: Diagnostic = {\r\n                    severity: toDiagnosticSeverity('error'),\r\n                    range,\r\n                    message: parserError.message,\r\n                    data: diagnosticData(DocumentValidator.ParsingError),\r\n                    source: this.getSource()\r\n                };\r\n                diagnostics.push(diagnostic);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected processLinkingErrors(document: LangiumDocument, diagnostics: Diagnostic[], _options: ValidationOptions): void {\r\n        for (const reference of document.references) {\r\n            const linkingError = reference.error;\r\n            if (linkingError) {\r\n                const info: DiagnosticInfo<AstNode, string> = {\r\n                    node: linkingError.container,\r\n                    property: linkingError.property,\r\n                    index: linkingError.index,\r\n                    data: {\r\n                        code: DocumentValidator.LinkingError,\r\n                        containerType: linkingError.container.$type,\r\n                        property: linkingError.property,\r\n                        refText: linkingError.reference.$refText\r\n                    } satisfies LinkingErrorData\r\n                };\r\n                diagnostics.push(this.toDiagnostic('error', linkingError.message, info));\r\n            }\r\n        }\r\n    }\r\n\r\n    protected async validateAst(rootNode: AstNode, options: ValidationOptions, cancelToken = CancellationToken.None): Promise<Diagnostic[]> {\r\n        const validationItems: Diagnostic[] = [];\r\n        const acceptor: ValidationAcceptor = <N extends AstNode>(severity: ValidationSeverity, message: string, info: DiagnosticInfo<N>) => {\r\n            validationItems.push(this.toDiagnostic(severity, message, info));\r\n        };\r\n\r\n        await this.validateAstBefore(rootNode, options, acceptor, cancelToken);\r\n        await this.validateAstNodes(rootNode, options, acceptor, cancelToken);\r\n        await this.validateAstAfter(rootNode, options, acceptor, cancelToken);\r\n\r\n        return validationItems;\r\n    }\r\n\r\n    protected async validateAstBefore(rootNode: AstNode, options: ValidationOptions, acceptor: ValidationAcceptor, cancelToken = CancellationToken.None): Promise<void> {\r\n        const checksBefore = this.validationRegistry.checksBefore;\r\n        for (const checkBefore of checksBefore) {\r\n            await interruptAndCheck(cancelToken);\r\n            await checkBefore(rootNode, acceptor, options.categories ?? [], cancelToken);\r\n        }\r\n    }\r\n\r\n    protected async validateAstNodes(rootNode: AstNode, options: ValidationOptions, acceptor: ValidationAcceptor, cancelToken = CancellationToken.None): Promise<void> {\r\n        await Promise.all(streamAst(rootNode).map(async node => {\r\n            await interruptAndCheck(cancelToken);\r\n            const checks = this.validationRegistry.getChecks(node.$type, options.categories);\r\n            for (const check of checks) {\r\n                await check(node, acceptor, cancelToken);\r\n            }\r\n        }));\r\n    }\r\n\r\n    protected async validateAstAfter(rootNode: AstNode, options: ValidationOptions, acceptor: ValidationAcceptor, cancelToken = CancellationToken.None): Promise<void> {\r\n        const checksAfter = this.validationRegistry.checksAfter;\r\n        for (const checkAfter of checksAfter) {\r\n            await interruptAndCheck(cancelToken);\r\n            await checkAfter(rootNode, acceptor, options.categories ?? [], cancelToken);\r\n        }\r\n    }\r\n\r\n    protected toDiagnostic<N extends AstNode>(severity: ValidationSeverity, message: string, info: DiagnosticInfo<N, string>): Diagnostic {\r\n        return {\r\n            message,\r\n            range: getDiagnosticRange(info),\r\n            severity: toDiagnosticSeverity(severity),\r\n            code: info.code,\r\n            codeDescription: info.codeDescription,\r\n            tags: info.tags,\r\n            relatedInformation: info.relatedInformation,\r\n            data: info.data,\r\n            source: this.getSource()\r\n        };\r\n    }\r\n\r\n    protected getSource(): string | undefined {\r\n        return this.metadata.languageId;\r\n    }\r\n}\r\n\r\nexport function getDiagnosticRange<N extends AstNode>(info: DiagnosticInfo<N, string>): Range {\r\n    if (info.range) {\r\n        return info.range;\r\n    }\r\n    let cstNode: CstNode | undefined;\r\n    if (typeof info.property === 'string') {\r\n        cstNode = findNodeForProperty(info.node.$cstNode, info.property, info.index);\r\n    } else if (typeof info.keyword === 'string') {\r\n        cstNode = findNodeForKeyword(info.node.$cstNode, info.keyword, info.index);\r\n    }\r\n    cstNode ??= info.node.$cstNode;\r\n    if (!cstNode) {\r\n        return {\r\n            start: { line: 0, character: 0 },\r\n            end: { line: 0, character: 0 }\r\n        };\r\n    }\r\n    return cstNode.range;\r\n}\r\n\r\n/**\r\n * Transforms the diagnostic severity from the {@link LexingDiagnosticSeverity} format to LSP's `DiagnosticSeverity` format.\r\n *\r\n * @param severity The lexing diagnostic severity\r\n * @returns Diagnostic severity according to `vscode-languageserver-types/lib/esm/main.js#DiagnosticSeverity`\r\n */\r\nexport function toDiagnosticSeverity(severity: LexingDiagnosticSeverity): DiagnosticSeverity {\r\n    switch (severity) {\r\n        case 'error':\r\n            return 1 satisfies typeof DiagnosticSeverity.Error;\r\n        case 'warning':\r\n            return 2 satisfies typeof DiagnosticSeverity.Warning;\r\n        case 'info':\r\n            return 3 satisfies typeof DiagnosticSeverity.Information;\r\n        case 'hint':\r\n            return 4 satisfies typeof DiagnosticSeverity.Hint;\r\n        default:\r\n            throw new Error('Invalid diagnostic severity: ' + severity);\r\n    }\r\n}\r\n\r\nexport function toDiagnosticData(severity: LexingDiagnosticSeverity): DiagnosticData {\r\n    switch (severity) {\r\n        case 'error':\r\n            return diagnosticData(DocumentValidator.LexingError);\r\n        case 'warning':\r\n            return diagnosticData(DocumentValidator.LexingWarning);\r\n        case 'info':\r\n            return diagnosticData(DocumentValidator.LexingInfo);\r\n        case 'hint':\r\n            return diagnosticData(DocumentValidator.LexingHint);\r\n        default:\r\n            throw new Error('Invalid diagnostic severity: ' + severity);\r\n    }\r\n}\r\n\r\nexport namespace DocumentValidator {\r\n    export const LexingError = 'lexing-error';\r\n    export const LexingWarning = 'lexing-warning';\r\n    export const LexingInfo = 'lexing-info';\r\n    export const LexingHint = 'lexing-hint';\r\n    export const ParsingError = 'parsing-error';\r\n    export const LinkingError = 'linking-error';\r\n}\r\n\r\nexport interface LinkingErrorData extends DiagnosticData {\r\n    containerType: string\r\n    property: string\r\n    refText: string\r\n}\r\n"], "mappings": "AAAA;;;;;AAcA,SAASA,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,kBAAkB,EAAEC,mBAAmB,QAAQ,2BAA2B;AACnF,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,iBAAiB,EAAEC,oBAAoB,QAAQ,2BAA2B;AACnF,SAASC,cAAc,QAAQ,0BAA0B;AAgCzD,OAAM,MAAOC,wBAAwB;EAKjCC,YAAYC,QAA6B;IACrC,IAAI,CAACC,kBAAkB,GAAGD,QAAQ,CAACE,UAAU,CAACC,kBAAkB;IAChE,IAAI,CAACC,QAAQ,GAAGJ,QAAQ,CAACK,gBAAgB;EAC7C;EAEA,MAAMC,gBAAgBA,CAACC,QAAyB,EAAEC,OAAA,GAA6B,EAAE,EAAEC,WAAW,GAAGnB,iBAAiB,CAACoB,IAAI;IACnH,MAAMC,WAAW,GAAGJ,QAAQ,CAACI,WAAW;IACxC,MAAMC,WAAW,GAAiB,EAAE;IAEpC,MAAMjB,iBAAiB,CAACc,WAAW,CAAC;IAEpC,IAAI,CAACD,OAAO,CAACK,UAAU,IAAIL,OAAO,CAACK,UAAU,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;MAChE,IAAI,CAACC,mBAAmB,CAACJ,WAAW,EAAEC,WAAW,EAAEJ,OAAO,CAAC;MAC3D,IAAIA,OAAO,CAACQ,qBAAqB,IAAIJ,WAAW,CAACK,IAAI,CAACC,CAAC,IAAG;QAAA,IAAAC,EAAA;QAAC,SAAAA,EAAA,GAAAD,CAAC,CAACE,IAAI,cAAAD,EAAA,uBAAAA,EAAA,CAAEE,IAAI,MAAKC,iBAAiB,CAACC,WAAW;MAAA,EAAC,EAAE;QACxG,OAAOX,WAAW;MACtB;MAEA,IAAI,CAACY,oBAAoB,CAACb,WAAW,EAAEC,WAAW,EAAEJ,OAAO,CAAC;MAC5D,IAAIA,OAAO,CAACiB,sBAAsB,IAAIb,WAAW,CAACK,IAAI,CAACC,CAAC,IAAG;QAAA,IAAAC,EAAA;QAAC,SAAAA,EAAA,GAAAD,CAAC,CAACE,IAAI,cAAAD,EAAA,uBAAAA,EAAA,CAAEE,IAAI,MAAKC,iBAAiB,CAACI,YAAY;MAAA,EAAC,EAAE;QAC1G,OAAOd,WAAW;MACtB;MAEA,IAAI,CAACe,oBAAoB,CAACpB,QAAQ,EAAEK,WAAW,EAAEJ,OAAO,CAAC;MACzD,IAAIA,OAAO,CAACoB,sBAAsB,IAAIhB,WAAW,CAACK,IAAI,CAACC,CAAC,IAAG;QAAA,IAAAC,EAAA;QAAC,SAAAA,EAAA,GAAAD,CAAC,CAACE,IAAI,cAAAD,EAAA,uBAAAA,EAAA,CAAEE,IAAI,MAAKC,iBAAiB,CAACO,YAAY;MAAA,EAAC,EAAE;QAC1G,OAAOjB,WAAW;MACtB;IACJ;IAEA;IACA,IAAI;MACAA,WAAW,CAACkB,IAAI,CAAC,IAAG,MAAM,IAAI,CAACC,WAAW,CAACpB,WAAW,CAACqB,KAAK,EAAExB,OAAO,EAAEC,WAAW,CAAC,EAAC;IACxF,CAAC,CAAC,OAAOwB,GAAG,EAAE;MACV,IAAIrC,oBAAoB,CAACqC,GAAG,CAAC,EAAE;QAC3B,MAAMA,GAAG;MACb;MACAC,OAAO,CAACC,KAAK,CAAC,sCAAsC,EAAEF,GAAG,CAAC;IAC9D;IAEA,MAAMtC,iBAAiB,CAACc,WAAW,CAAC;IAEpC,OAAOG,WAAW;EACtB;EAEUG,mBAAmBA,CAACJ,WAAwB,EAAEC,WAAyB,EAAEwB,QAA2B;;IAC1G,MAAMC,gBAAgB,GAAG,CAAC,GAAG1B,WAAW,CAAC2B,WAAW,EAAE,IAAG,CAAAC,EAAA,IAAApB,EAAA,GAAAR,WAAW,CAAC6B,WAAW,cAAArB,EAAA,uBAAAA,EAAA,CAAEP,WAAW,cAAA2B,EAAA,cAAAA,EAAA,GAAI,EAAE,EAAuB;IAC1H,KAAK,MAAME,eAAe,IAAIJ,gBAAgB,EAAE;MAC5C,MAAMK,QAAQ,GAAG,CAAAC,EAAA,GAAAF,eAAe,CAACC,QAAQ,cAAAC,EAAA,cAAAA,EAAA,GAAI,OAAO;MACpD,MAAMC,UAAU,GAAe;QAC3BF,QAAQ,EAAEG,oBAAoB,CAACH,QAAQ,CAAC;QACxCI,KAAK,EAAE;UACHC,KAAK,EAAE;YACHC,IAAI,EAAEP,eAAe,CAACO,IAAK,GAAG,CAAC;YAC/BC,SAAS,EAAER,eAAe,CAACS,MAAO,GAAG;WACxC;UACDC,GAAG,EAAE;YACDH,IAAI,EAAEP,eAAe,CAACO,IAAK,GAAG,CAAC;YAC/BC,SAAS,EAAER,eAAe,CAACS,MAAO,GAAGT,eAAe,CAACW,MAAM,GAAG;;SAErE;QACDC,OAAO,EAAEZ,eAAe,CAACY,OAAO;QAChCjC,IAAI,EAAEkC,gBAAgB,CAACZ,QAAQ,CAAC;QAChCa,MAAM,EAAE,IAAI,CAACC,SAAS;OACzB;MACD5C,WAAW,CAACkB,IAAI,CAACc,UAAU,CAAC;IAChC;EACJ;EAEUpB,oBAAoBA,CAACb,WAAwB,EAAEC,WAAyB,EAAEwB,QAA2B;IAC3G,KAAK,MAAMqB,WAAW,IAAI9C,WAAW,CAAC+C,YAAY,EAAE;MAChD,IAAIZ,KAAK,GAAsBa,SAAS;MACxC;MACA;MACA;MACA,IAAIC,KAAK,CAACH,WAAW,CAACI,KAAK,CAACC,WAAW,CAAC,EAAE;QACtC;QACA;QACA,IAAI,eAAe,IAAIL,WAAW,EAAE;UAChC,MAAMI,KAAK,GAAIJ,WAAwC,CAACM,aAAa;UACrE,IAAI,CAACH,KAAK,CAACC,KAAK,CAACC,WAAW,CAAC,EAAE;YAC3B,MAAME,QAAQ,GAAa;cAAEhB,IAAI,EAAEa,KAAK,CAACI,OAAQ,GAAG,CAAC;cAAEhB,SAAS,EAAEY,KAAK,CAACK;YAAU,CAAE;YACpFpB,KAAK,GAAG;cAAEC,KAAK,EAAEiB,QAAQ;cAAEb,GAAG,EAAEa;YAAQ,CAAC;UAC7C,CAAC,MAAM;YACH;YACA;YACA,MAAMA,QAAQ,GAAa;cAAEhB,IAAI,EAAE,CAAC;cAAEC,SAAS,EAAE;YAAC,CAAE;YACpDH,KAAK,GAAG;cAAEC,KAAK,EAAEiB,QAAQ;cAAEb,GAAG,EAAEa;YAAQ,CAAC;UAC7C;QACJ;MACJ,CAAC,MAAM;QACHlB,KAAK,GAAGpD,YAAY,CAAC+D,WAAW,CAACI,KAAK,CAAC;MAC3C;MACA,IAAIf,KAAK,EAAE;QACP,MAAMF,UAAU,GAAe;UAC3BF,QAAQ,EAAEG,oBAAoB,CAAC,OAAO,CAAC;UACvCC,KAAK;UACLO,OAAO,EAAEI,WAAW,CAACJ,OAAO;UAC5BjC,IAAI,EAAEvB,cAAc,CAACyB,iBAAiB,CAACI,YAAY,CAAC;UACpD6B,MAAM,EAAE,IAAI,CAACC,SAAS;SACzB;QACD5C,WAAW,CAACkB,IAAI,CAACc,UAAU,CAAC;MAChC;IACJ;EACJ;EAEUjB,oBAAoBA,CAACpB,QAAyB,EAAEK,WAAyB,EAAEwB,QAA2B;IAC5G,KAAK,MAAM+B,SAAS,IAAI5D,QAAQ,CAAC6D,UAAU,EAAE;MACzC,MAAMC,YAAY,GAAGF,SAAS,CAAChC,KAAK;MACpC,IAAIkC,YAAY,EAAE;QACd,MAAMC,IAAI,GAAoC;UAC1CC,IAAI,EAAEF,YAAY,CAACG,SAAS;UAC5BC,QAAQ,EAAEJ,YAAY,CAACI,QAAQ;UAC/BC,KAAK,EAAEL,YAAY,CAACK,KAAK;UACzBtD,IAAI,EAAE;YACFC,IAAI,EAAEC,iBAAiB,CAACO,YAAY;YACpC8C,aAAa,EAAEN,YAAY,CAACG,SAAS,CAACI,KAAK;YAC3CH,QAAQ,EAAEJ,YAAY,CAACI,QAAQ;YAC/BI,OAAO,EAAER,YAAY,CAACF,SAAS,CAACW;;SAEvC;QACDlE,WAAW,CAACkB,IAAI,CAAC,IAAI,CAACiD,YAAY,CAAC,OAAO,EAAEV,YAAY,CAAChB,OAAO,EAAEiB,IAAI,CAAC,CAAC;MAC5E;IACJ;EACJ;EAEU,MAAMvC,WAAWA,CAACiD,QAAiB,EAAExE,OAA0B,EAAEC,WAAW,GAAGnB,iBAAiB,CAACoB,IAAI;IAC3G,MAAMuE,eAAe,GAAiB,EAAE;IACxC,MAAMC,QAAQ,GAAuBA,CAAoBxC,QAA4B,EAAEW,OAAe,EAAEiB,IAAuB,KAAI;MAC/HW,eAAe,CAACnD,IAAI,CAAC,IAAI,CAACiD,YAAY,CAACrC,QAAQ,EAAEW,OAAO,EAAEiB,IAAI,CAAC,CAAC;IACpE,CAAC;IAED,MAAM,IAAI,CAACa,iBAAiB,CAACH,QAAQ,EAAExE,OAAO,EAAE0E,QAAQ,EAAEzE,WAAW,CAAC;IACtE,MAAM,IAAI,CAAC2E,gBAAgB,CAACJ,QAAQ,EAAExE,OAAO,EAAE0E,QAAQ,EAAEzE,WAAW,CAAC;IACrE,MAAM,IAAI,CAAC4E,gBAAgB,CAACL,QAAQ,EAAExE,OAAO,EAAE0E,QAAQ,EAAEzE,WAAW,CAAC;IAErE,OAAOwE,eAAe;EAC1B;EAEU,MAAME,iBAAiBA,CAACH,QAAiB,EAAExE,OAA0B,EAAE0E,QAA4B,EAAEzE,WAAW,GAAGnB,iBAAiB,CAACoB,IAAI;;IAC/I,MAAM4E,YAAY,GAAG,IAAI,CAACrF,kBAAkB,CAACqF,YAAY;IACzD,KAAK,MAAMC,WAAW,IAAID,YAAY,EAAE;MACpC,MAAM3F,iBAAiB,CAACc,WAAW,CAAC;MACpC,MAAM8E,WAAW,CAACP,QAAQ,EAAEE,QAAQ,EAAE,CAAA/D,EAAA,GAAAX,OAAO,CAACK,UAAU,cAAAM,EAAA,cAAAA,EAAA,GAAI,EAAE,EAAEV,WAAW,CAAC;IAChF;EACJ;EAEU,MAAM2E,gBAAgBA,CAACJ,QAAiB,EAAExE,OAA0B,EAAE0E,QAA4B,EAAEzE,WAAW,GAAGnB,iBAAiB,CAACoB,IAAI;IAC9I,MAAM8E,OAAO,CAACC,GAAG,CAAChG,SAAS,CAACuF,QAAQ,CAAC,CAACU,GAAG,CAAC,MAAMnB,IAAI,IAAG;MACnD,MAAM5E,iBAAiB,CAACc,WAAW,CAAC;MACpC,MAAMkF,MAAM,GAAG,IAAI,CAAC1F,kBAAkB,CAAC2F,SAAS,CAACrB,IAAI,CAACK,KAAK,EAAEpE,OAAO,CAACK,UAAU,CAAC;MAChF,KAAK,MAAMgF,KAAK,IAAIF,MAAM,EAAE;QACxB,MAAME,KAAK,CAACtB,IAAI,EAAEW,QAAQ,EAAEzE,WAAW,CAAC;MAC5C;IACJ,CAAC,CAAC,CAAC;EACP;EAEU,MAAM4E,gBAAgBA,CAACL,QAAiB,EAAExE,OAA0B,EAAE0E,QAA4B,EAAEzE,WAAW,GAAGnB,iBAAiB,CAACoB,IAAI;;IAC9I,MAAMoF,WAAW,GAAG,IAAI,CAAC7F,kBAAkB,CAAC6F,WAAW;IACvD,KAAK,MAAMC,UAAU,IAAID,WAAW,EAAE;MAClC,MAAMnG,iBAAiB,CAACc,WAAW,CAAC;MACpC,MAAMsF,UAAU,CAACf,QAAQ,EAAEE,QAAQ,EAAE,CAAA/D,EAAA,GAAAX,OAAO,CAACK,UAAU,cAAAM,EAAA,cAAAA,EAAA,GAAI,EAAE,EAAEV,WAAW,CAAC;IAC/E;EACJ;EAEUsE,YAAYA,CAAoBrC,QAA4B,EAAEW,OAAe,EAAEiB,IAA+B;IACpH,OAAO;MACHjB,OAAO;MACPP,KAAK,EAAEkD,kBAAkB,CAAC1B,IAAI,CAAC;MAC/B5B,QAAQ,EAAEG,oBAAoB,CAACH,QAAQ,CAAC;MACxCrB,IAAI,EAAEiD,IAAI,CAACjD,IAAI;MACf4E,eAAe,EAAE3B,IAAI,CAAC2B,eAAe;MACrCC,IAAI,EAAE5B,IAAI,CAAC4B,IAAI;MACfC,kBAAkB,EAAE7B,IAAI,CAAC6B,kBAAkB;MAC3C/E,IAAI,EAAEkD,IAAI,CAAClD,IAAI;MACfmC,MAAM,EAAE,IAAI,CAACC,SAAS;KACzB;EACL;EAEUA,SAASA,CAAA;IACf,OAAO,IAAI,CAACpD,QAAQ,CAACgG,UAAU;EACnC;;AAGJ,OAAM,SAAUJ,kBAAkBA,CAAoB1B,IAA+B;EACjF,IAAIA,IAAI,CAACxB,KAAK,EAAE;IACZ,OAAOwB,IAAI,CAACxB,KAAK;EACrB;EACA,IAAIuD,OAA4B;EAChC,IAAI,OAAO/B,IAAI,CAACG,QAAQ,KAAK,QAAQ,EAAE;IACnC4B,OAAO,GAAG7G,mBAAmB,CAAC8E,IAAI,CAACC,IAAI,CAAC+B,QAAQ,EAAEhC,IAAI,CAACG,QAAQ,EAAEH,IAAI,CAACI,KAAK,CAAC;EAChF,CAAC,MAAM,IAAI,OAAOJ,IAAI,CAACiC,OAAO,KAAK,QAAQ,EAAE;IACzCF,OAAO,GAAG9G,kBAAkB,CAAC+E,IAAI,CAACC,IAAI,CAAC+B,QAAQ,EAAEhC,IAAI,CAACiC,OAAO,EAAEjC,IAAI,CAACI,KAAK,CAAC;EAC9E;EACA2B,OAAO,aAAPA,OAAO,cAAPA,OAAO,GAAPA,OAAO,GAAK/B,IAAI,CAACC,IAAI,CAAC+B,QAAQ;EAC9B,IAAI,CAACD,OAAO,EAAE;IACV,OAAO;MACHtD,KAAK,EAAE;QAAEC,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAC,CAAE;MAChCE,GAAG,EAAE;QAAEH,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAC;KAC/B;EACL;EACA,OAAOoD,OAAO,CAACvD,KAAK;AACxB;AAEA;;;;;;AAMA,OAAM,SAAUD,oBAAoBA,CAACH,QAAkC;EACnE,QAAQA,QAAQ;IACZ,KAAK,OAAO;MACR,OAAO,CAA2C;IACtD,KAAK,SAAS;MACV,OAAO,CAA6C;IACxD,KAAK,MAAM;MACP,OAAO,CAAiD;IAC5D,KAAK,MAAM;MACP,OAAO,CAA0C;IACrD;MACI,MAAM,IAAI8D,KAAK,CAAC,+BAA+B,GAAG9D,QAAQ,CAAC;EACnE;AACJ;AAEA,OAAM,SAAUY,gBAAgBA,CAACZ,QAAkC;EAC/D,QAAQA,QAAQ;IACZ,KAAK,OAAO;MACR,OAAO7C,cAAc,CAACyB,iBAAiB,CAACC,WAAW,CAAC;IACxD,KAAK,SAAS;MACV,OAAO1B,cAAc,CAACyB,iBAAiB,CAACmF,aAAa,CAAC;IAC1D,KAAK,MAAM;MACP,OAAO5G,cAAc,CAACyB,iBAAiB,CAACoF,UAAU,CAAC;IACvD,KAAK,MAAM;MACP,OAAO7G,cAAc,CAACyB,iBAAiB,CAACqF,UAAU,CAAC;IACvD;MACI,MAAM,IAAIH,KAAK,CAAC,+BAA+B,GAAG9D,QAAQ,CAAC;EACnE;AACJ;AAEA,OAAM,IAAWpB,iBAAiB;AAAlC,WAAiBA,iBAAiB;EACjBA,iBAAA,CAAAC,WAAW,GAAG,cAAc;EAC5BD,iBAAA,CAAAmF,aAAa,GAAG,gBAAgB;EAChCnF,iBAAA,CAAAoF,UAAU,GAAG,aAAa;EAC1BpF,iBAAA,CAAAqF,UAAU,GAAG,aAAa;EAC1BrF,iBAAA,CAAAI,YAAY,GAAG,eAAe;EAC9BJ,iBAAA,CAAAO,YAAY,GAAG,eAAe;AAC/C,CAAC,EAPgBP,iBAAiB,KAAjBA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}