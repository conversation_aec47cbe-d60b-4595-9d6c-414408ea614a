{"ast": null, "code": "import * as _ from 'lodash-es';\nimport { PriorityQueue } from '../data/priority-queue.js';\nexport { dijkstra };\nvar DEFAULT_WEIGHT_FUNC = _.constant(1);\nfunction dijkstra(g, source, weightFn, edgeFn) {\n  return runDijkstra(g, String(source), weightFn || DEFAULT_WEIGHT_FUNC, edgeFn || function (v) {\n    return g.outEdges(v);\n  });\n}\nfunction runDijkstra(g, source, weightFn, edgeFn) {\n  var results = {};\n  var pq = new PriorityQueue();\n  var v, vEntry;\n  var updateNeighbors = function (edge) {\n    var w = edge.v !== v ? edge.v : edge.w;\n    var wEntry = results[w];\n    var weight = weightFn(edge);\n    var distance = vEntry.distance + weight;\n    if (weight < 0) {\n      throw new Error('dijkstra does not allow negative edge weights. ' + 'Bad edge: ' + edge + ' Weight: ' + weight);\n    }\n    if (distance < wEntry.distance) {\n      wEntry.distance = distance;\n      wEntry.predecessor = v;\n      pq.decrease(w, distance);\n    }\n  };\n  g.nodes().forEach(function (v) {\n    var distance = v === source ? 0 : Number.POSITIVE_INFINITY;\n    results[v] = {\n      distance: distance\n    };\n    pq.add(v, distance);\n  });\n  while (pq.size() > 0) {\n    v = pq.removeMin();\n    vEntry = results[v];\n    if (vEntry.distance === Number.POSITIVE_INFINITY) {\n      break;\n    }\n    edgeFn(v).forEach(updateNeighbors);\n  }\n  return results;\n}", "map": {"version": 3, "names": ["_", "PriorityQueue", "<PERSON><PERSON><PERSON>", "DEFAULT_WEIGHT_FUNC", "constant", "g", "source", "weightFn", "edgeFn", "runDijkstra", "String", "v", "outEdges", "results", "pq", "vEntry", "updateNeighbors", "edge", "w", "wEntry", "weight", "distance", "Error", "predecessor", "decrease", "nodes", "for<PERSON>ach", "Number", "POSITIVE_INFINITY", "add", "size", "removeMin"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/node_modules/dagre-d3-es/src/graphlib/alg/dijkstra.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { PriorityQueue } from '../data/priority-queue.js';\n\nexport { dijkstra };\n\nvar DEFAULT_WEIGHT_FUNC = _.constant(1);\n\nfunction dijkstra(g, source, weightFn, edgeFn) {\n  return runDijkstra(\n    g,\n    String(source),\n    weightFn || DEFAULT_WEIGHT_FUNC,\n    edgeFn ||\n      function (v) {\n        return g.outEdges(v);\n      },\n  );\n}\n\nfunction runDijkstra(g, source, weightFn, edgeFn) {\n  var results = {};\n  var pq = new PriorityQueue();\n  var v, vEntry;\n\n  var updateNeighbors = function (edge) {\n    var w = edge.v !== v ? edge.v : edge.w;\n    var wEntry = results[w];\n    var weight = weightFn(edge);\n    var distance = vEntry.distance + weight;\n\n    if (weight < 0) {\n      throw new Error(\n        'dijkstra does not allow negative edge weights. ' +\n          'Bad edge: ' +\n          edge +\n          ' Weight: ' +\n          weight,\n      );\n    }\n\n    if (distance < wEntry.distance) {\n      wEntry.distance = distance;\n      wEntry.predecessor = v;\n      pq.decrease(w, distance);\n    }\n  };\n\n  g.nodes().forEach(function (v) {\n    var distance = v === source ? 0 : Number.POSITIVE_INFINITY;\n    results[v] = { distance: distance };\n    pq.add(v, distance);\n  });\n\n  while (pq.size() > 0) {\n    v = pq.removeMin();\n    vEntry = results[v];\n    if (vEntry.distance === Number.POSITIVE_INFINITY) {\n      break;\n    }\n\n    edgeFn(v).forEach(updateNeighbors);\n  }\n\n  return results;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,CAAC,MAAM,WAAW;AAC9B,SAASC,aAAa,QAAQ,2BAA2B;AAEzD,SAASC,QAAQ;AAEjB,IAAIC,mBAAmB,GAAGH,CAAC,CAACI,QAAQ,CAAC,CAAC,CAAC;AAEvC,SAASF,QAAQA,CAACG,CAAC,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EAC7C,OAAOC,WAAW,CAChBJ,CAAC,EACDK,MAAM,CAACJ,MAAM,CAAC,EACdC,QAAQ,IAAIJ,mBAAmB,EAC/BK,MAAM,IACJ,UAAUG,CAAC,EAAE;IACX,OAAON,CAAC,CAACO,QAAQ,CAACD,CAAC,CAAC;EACtB,CACJ,CAAC;AACH;AAEA,SAASF,WAAWA,CAACJ,CAAC,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EAChD,IAAIK,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIC,EAAE,GAAG,IAAIb,aAAa,CAAC,CAAC;EAC5B,IAAIU,CAAC,EAAEI,MAAM;EAEb,IAAIC,eAAe,GAAG,SAAAA,CAAUC,IAAI,EAAE;IACpC,IAAIC,CAAC,GAAGD,IAAI,CAACN,CAAC,KAAKA,CAAC,GAAGM,IAAI,CAACN,CAAC,GAAGM,IAAI,CAACC,CAAC;IACtC,IAAIC,MAAM,GAAGN,OAAO,CAACK,CAAC,CAAC;IACvB,IAAIE,MAAM,GAAGb,QAAQ,CAACU,IAAI,CAAC;IAC3B,IAAII,QAAQ,GAAGN,MAAM,CAACM,QAAQ,GAAGD,MAAM;IAEvC,IAAIA,MAAM,GAAG,CAAC,EAAE;MACd,MAAM,IAAIE,KAAK,CACb,iDAAiD,GAC/C,YAAY,GACZL,IAAI,GACJ,WAAW,GACXG,MACJ,CAAC;IACH;IAEA,IAAIC,QAAQ,GAAGF,MAAM,CAACE,QAAQ,EAAE;MAC9BF,MAAM,CAACE,QAAQ,GAAGA,QAAQ;MAC1BF,MAAM,CAACI,WAAW,GAAGZ,CAAC;MACtBG,EAAE,CAACU,QAAQ,CAACN,CAAC,EAAEG,QAAQ,CAAC;IAC1B;EACF,CAAC;EAEDhB,CAAC,CAACoB,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUf,CAAC,EAAE;IAC7B,IAAIU,QAAQ,GAAGV,CAAC,KAAKL,MAAM,GAAG,CAAC,GAAGqB,MAAM,CAACC,iBAAiB;IAC1Df,OAAO,CAACF,CAAC,CAAC,GAAG;MAAEU,QAAQ,EAAEA;IAAS,CAAC;IACnCP,EAAE,CAACe,GAAG,CAAClB,CAAC,EAAEU,QAAQ,CAAC;EACrB,CAAC,CAAC;EAEF,OAAOP,EAAE,CAACgB,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE;IACpBnB,CAAC,GAAGG,EAAE,CAACiB,SAAS,CAAC,CAAC;IAClBhB,MAAM,GAAGF,OAAO,CAACF,CAAC,CAAC;IACnB,IAAII,MAAM,CAACM,QAAQ,KAAKM,MAAM,CAACC,iBAAiB,EAAE;MAChD;IACF;IAEApB,MAAM,CAACG,CAAC,CAAC,CAACe,OAAO,CAACV,eAAe,CAAC;EACpC;EAEA,OAAOH,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}