{"ast": null, "code": "import * as _ from 'lodash-es';\nimport { dijkstra } from './dijkstra.js';\nexport { dijkstraAll };\nfunction dijkstraAll(g, weightFunc, edgeFunc) {\n  return _.transform(g.nodes(), function (acc, v) {\n    acc[v] = dijkstra(g, v, weightFunc, edgeFunc);\n  }, {});\n}", "map": {"version": 3, "names": ["_", "<PERSON><PERSON><PERSON>", "dijkstraAll", "g", "weightFunc", "edgeFunc", "transform", "nodes", "acc", "v"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/node_modules/dagre-d3-es/src/graphlib/alg/dijkstra-all.js"], "sourcesContent": ["import * as _ from 'lodash-es';\nimport { dijkstra } from './dijkstra.js';\n\nexport { dijkstraAll };\n\nfunction dijkstraAll(g, weightFunc, edgeFunc) {\n  return _.transform(\n    g.nodes(),\n    function (acc, v) {\n      acc[v] = dijkstra(g, v, weightFunc, edgeFunc);\n    },\n    {},\n  );\n}\n"], "mappings": "AAAA,OAAO,KAAKA,CAAC,MAAM,WAAW;AAC9B,SAASC,QAAQ,QAAQ,eAAe;AAExC,SAASC,WAAW;AAEpB,SAASA,WAAWA,CAACC,CAAC,EAAEC,UAAU,EAAEC,QAAQ,EAAE;EAC5C,OAAOL,CAAC,CAACM,SAAS,CAChBH,CAAC,CAACI,KAAK,CAAC,CAAC,EACT,UAAUC,GAAG,EAAEC,CAAC,EAAE;IAChBD,GAAG,CAACC,CAAC,CAAC,GAAGR,QAAQ,CAACE,CAAC,EAAEM,CAAC,EAAEL,UAAU,EAAEC,QAAQ,CAAC;EAC/C,CAAC,EACD,CAAC,CACH,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}