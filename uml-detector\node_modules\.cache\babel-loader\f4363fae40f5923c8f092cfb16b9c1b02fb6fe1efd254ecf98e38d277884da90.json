{"ast": null, "code": "import { createTokenInstance, EOF, tokenMatcher } from \"../../../scan/tokens_public.js\";\nimport { clone, dropRight, find, flatten, has, includes, isEmpty, map } from \"lodash-es\";\nimport { MismatchedTokenException } from \"../../exceptions_public.js\";\nimport { IN } from \"../../constants.js\";\nimport { DEFAULT_PARSER_CONFIG } from \"../parser.js\";\nexport const EOF_FOLLOW_KEY = {};\nexport const IN_RULE_RECOVERY_EXCEPTION = \"InRuleRecoveryException\";\nexport class InRuleRecoveryException extends Error {\n  constructor(message) {\n    super(message);\n    this.name = IN_RULE_RECOVERY_EXCEPTION;\n  }\n}\n/**\n * This trait is responsible for the error recovery and fault tolerant logic\n */\nexport class Recoverable {\n  initRecoverable(config) {\n    this.firstAfterRepMap = {};\n    this.resyncFollows = {};\n    this.recoveryEnabled = has(config, \"recoveryEnabled\") ? config.recoveryEnabled // assumes end user provides the correct config value/type\n    : DEFAULT_PARSER_CONFIG.recoveryEnabled;\n    // performance optimization, NOOP will be inlined which\n    // effectively means that this optional feature does not exist\n    // when not used.\n    if (this.recoveryEnabled) {\n      this.attemptInRepetitionRecovery = attemptInRepetitionRecovery;\n    }\n  }\n  getTokenToInsert(tokType) {\n    const tokToInsert = createTokenInstance(tokType, \"\", NaN, NaN, NaN, NaN, NaN, NaN);\n    tokToInsert.isInsertedInRecovery = true;\n    return tokToInsert;\n  }\n  canTokenTypeBeInsertedInRecovery(tokType) {\n    return true;\n  }\n  canTokenTypeBeDeletedInRecovery(tokType) {\n    return true;\n  }\n  tryInRepetitionRecovery(grammarRule, grammarRuleArgs, lookAheadFunc, expectedTokType) {\n    // TODO: can the resyncTokenType be cached?\n    const reSyncTokType = this.findReSyncTokenType();\n    const savedLexerState = this.exportLexerState();\n    const resyncedTokens = [];\n    let passedResyncPoint = false;\n    const nextTokenWithoutResync = this.LA(1);\n    let currToken = this.LA(1);\n    const generateErrorMessage = () => {\n      const previousToken = this.LA(0);\n      // we are preemptively re-syncing before an error has been detected, therefor we must reproduce\n      // the error that would have been thrown\n      const msg = this.errorMessageProvider.buildMismatchTokenMessage({\n        expected: expectedTokType,\n        actual: nextTokenWithoutResync,\n        previous: previousToken,\n        ruleName: this.getCurrRuleFullName()\n      });\n      const error = new MismatchedTokenException(msg, nextTokenWithoutResync, this.LA(0));\n      // the first token here will be the original cause of the error, this is not part of the resyncedTokens property.\n      error.resyncedTokens = dropRight(resyncedTokens);\n      this.SAVE_ERROR(error);\n    };\n    while (!passedResyncPoint) {\n      // re-synced to a point where we can safely exit the repetition/\n      if (this.tokenMatcher(currToken, expectedTokType)) {\n        generateErrorMessage();\n        return; // must return here to avoid reverting the inputIdx\n      } else if (lookAheadFunc.call(this)) {\n        // we skipped enough tokens so we can resync right back into another iteration of the repetition grammar rule\n        generateErrorMessage();\n        // recursive invocation in other to support multiple re-syncs in the same top level repetition grammar rule\n        grammarRule.apply(this, grammarRuleArgs);\n        return; // must return here to avoid reverting the inputIdx\n      } else if (this.tokenMatcher(currToken, reSyncTokType)) {\n        passedResyncPoint = true;\n      } else {\n        currToken = this.SKIP_TOKEN();\n        this.addToResyncTokens(currToken, resyncedTokens);\n      }\n    }\n    // we were unable to find a CLOSER point to resync inside the Repetition, reset the state.\n    // The parsing exception we were trying to prevent will happen in the NEXT parsing step. it may be handled by\n    // \"between rules\" resync recovery later in the flow.\n    this.importLexerState(savedLexerState);\n  }\n  shouldInRepetitionRecoveryBeTried(expectTokAfterLastMatch, nextTokIdx, notStuck) {\n    // Edge case of arriving from a MANY repetition which is stuck\n    // Attempting recovery in this case could cause an infinite loop\n    if (notStuck === false) {\n      return false;\n    }\n    // no need to recover, next token is what we expect...\n    if (this.tokenMatcher(this.LA(1), expectTokAfterLastMatch)) {\n      return false;\n    }\n    // error recovery is disabled during backtracking as it can make the parser ignore a valid grammar path\n    // and prefer some backtracking path that includes recovered errors.\n    if (this.isBackTracking()) {\n      return false;\n    }\n    // if we can perform inRule recovery (single token insertion or deletion) we always prefer that recovery algorithm\n    // because if it works, it makes the least amount of changes to the input stream (greedy algorithm)\n    //noinspection RedundantIfStatementJS\n    if (this.canPerformInRuleRecovery(expectTokAfterLastMatch, this.getFollowsForInRuleRecovery(expectTokAfterLastMatch, nextTokIdx))) {\n      return false;\n    }\n    return true;\n  }\n  // Error Recovery functionality\n  getFollowsForInRuleRecovery(tokType, tokIdxInRule) {\n    const grammarPath = this.getCurrentGrammarPath(tokType, tokIdxInRule);\n    const follows = this.getNextPossibleTokenTypes(grammarPath);\n    return follows;\n  }\n  tryInRuleRecovery(expectedTokType, follows) {\n    if (this.canRecoverWithSingleTokenInsertion(expectedTokType, follows)) {\n      const tokToInsert = this.getTokenToInsert(expectedTokType);\n      return tokToInsert;\n    }\n    if (this.canRecoverWithSingleTokenDeletion(expectedTokType)) {\n      const nextTok = this.SKIP_TOKEN();\n      this.consumeToken();\n      return nextTok;\n    }\n    throw new InRuleRecoveryException(\"sad sad panda\");\n  }\n  canPerformInRuleRecovery(expectedToken, follows) {\n    return this.canRecoverWithSingleTokenInsertion(expectedToken, follows) || this.canRecoverWithSingleTokenDeletion(expectedToken);\n  }\n  canRecoverWithSingleTokenInsertion(expectedTokType, follows) {\n    if (!this.canTokenTypeBeInsertedInRecovery(expectedTokType)) {\n      return false;\n    }\n    // must know the possible following tokens to perform single token insertion\n    if (isEmpty(follows)) {\n      return false;\n    }\n    const mismatchedTok = this.LA(1);\n    const isMisMatchedTokInFollows = find(follows, possibleFollowsTokType => {\n      return this.tokenMatcher(mismatchedTok, possibleFollowsTokType);\n    }) !== undefined;\n    return isMisMatchedTokInFollows;\n  }\n  canRecoverWithSingleTokenDeletion(expectedTokType) {\n    if (!this.canTokenTypeBeDeletedInRecovery(expectedTokType)) {\n      return false;\n    }\n    const isNextTokenWhatIsExpected = this.tokenMatcher(this.LA(2), expectedTokType);\n    return isNextTokenWhatIsExpected;\n  }\n  isInCurrentRuleReSyncSet(tokenTypeIdx) {\n    const followKey = this.getCurrFollowKey();\n    const currentRuleReSyncSet = this.getFollowSetFromFollowKey(followKey);\n    return includes(currentRuleReSyncSet, tokenTypeIdx);\n  }\n  findReSyncTokenType() {\n    const allPossibleReSyncTokTypes = this.flattenFollowSet();\n    // this loop will always terminate as EOF is always in the follow stack and also always (virtually) in the input\n    let nextToken = this.LA(1);\n    let k = 2;\n    while (true) {\n      const foundMatch = find(allPossibleReSyncTokTypes, resyncTokType => {\n        const canMatch = tokenMatcher(nextToken, resyncTokType);\n        return canMatch;\n      });\n      if (foundMatch !== undefined) {\n        return foundMatch;\n      }\n      nextToken = this.LA(k);\n      k++;\n    }\n  }\n  getCurrFollowKey() {\n    // the length is at least one as we always add the ruleName to the stack before invoking the rule.\n    if (this.RULE_STACK.length === 1) {\n      return EOF_FOLLOW_KEY;\n    }\n    const currRuleShortName = this.getLastExplicitRuleShortName();\n    const currRuleIdx = this.getLastExplicitRuleOccurrenceIndex();\n    const prevRuleShortName = this.getPreviousExplicitRuleShortName();\n    return {\n      ruleName: this.shortRuleNameToFullName(currRuleShortName),\n      idxInCallingRule: currRuleIdx,\n      inRule: this.shortRuleNameToFullName(prevRuleShortName)\n    };\n  }\n  buildFullFollowKeyStack() {\n    const explicitRuleStack = this.RULE_STACK;\n    const explicitOccurrenceStack = this.RULE_OCCURRENCE_STACK;\n    return map(explicitRuleStack, (ruleName, idx) => {\n      if (idx === 0) {\n        return EOF_FOLLOW_KEY;\n      }\n      return {\n        ruleName: this.shortRuleNameToFullName(ruleName),\n        idxInCallingRule: explicitOccurrenceStack[idx],\n        inRule: this.shortRuleNameToFullName(explicitRuleStack[idx - 1])\n      };\n    });\n  }\n  flattenFollowSet() {\n    const followStack = map(this.buildFullFollowKeyStack(), currKey => {\n      return this.getFollowSetFromFollowKey(currKey);\n    });\n    return flatten(followStack);\n  }\n  getFollowSetFromFollowKey(followKey) {\n    if (followKey === EOF_FOLLOW_KEY) {\n      return [EOF];\n    }\n    const followName = followKey.ruleName + followKey.idxInCallingRule + IN + followKey.inRule;\n    return this.resyncFollows[followName];\n  }\n  // It does not make any sense to include a virtual EOF token in the list of resynced tokens\n  // as EOF does not really exist and thus does not contain any useful information (line/column numbers)\n  addToResyncTokens(token, resyncTokens) {\n    if (!this.tokenMatcher(token, EOF)) {\n      resyncTokens.push(token);\n    }\n    return resyncTokens;\n  }\n  reSyncTo(tokType) {\n    const resyncedTokens = [];\n    let nextTok = this.LA(1);\n    while (this.tokenMatcher(nextTok, tokType) === false) {\n      nextTok = this.SKIP_TOKEN();\n      this.addToResyncTokens(nextTok, resyncedTokens);\n    }\n    // the last token is not part of the error.\n    return dropRight(resyncedTokens);\n  }\n  attemptInRepetitionRecovery(prodFunc, args, lookaheadFunc, dslMethodIdx, prodOccurrence, nextToksWalker, notStuck) {\n    // by default this is a NO-OP\n    // The actual implementation is with the function(not method) below\n  }\n  getCurrentGrammarPath(tokType, tokIdxInRule) {\n    const pathRuleStack = this.getHumanReadableRuleStack();\n    const pathOccurrenceStack = clone(this.RULE_OCCURRENCE_STACK);\n    const grammarPath = {\n      ruleStack: pathRuleStack,\n      occurrenceStack: pathOccurrenceStack,\n      lastTok: tokType,\n      lastTokOccurrence: tokIdxInRule\n    };\n    return grammarPath;\n  }\n  getHumanReadableRuleStack() {\n    return map(this.RULE_STACK, currShortName => this.shortRuleNameToFullName(currShortName));\n  }\n}\nexport function attemptInRepetitionRecovery(prodFunc, args, lookaheadFunc, dslMethodIdx, prodOccurrence, nextToksWalker, notStuck) {\n  const key = this.getKeyForAutomaticLookahead(dslMethodIdx, prodOccurrence);\n  let firstAfterRepInfo = this.firstAfterRepMap[key];\n  if (firstAfterRepInfo === undefined) {\n    const currRuleName = this.getCurrRuleFullName();\n    const ruleGrammar = this.getGAstProductions()[currRuleName];\n    const walker = new nextToksWalker(ruleGrammar, prodOccurrence);\n    firstAfterRepInfo = walker.startWalking();\n    this.firstAfterRepMap[key] = firstAfterRepInfo;\n  }\n  let expectTokAfterLastMatch = firstAfterRepInfo.token;\n  let nextTokIdx = firstAfterRepInfo.occurrence;\n  const isEndOfRule = firstAfterRepInfo.isEndOfRule;\n  // special edge case of a TOP most repetition after which the input should END.\n  // this will force an attempt for inRule recovery in that scenario.\n  if (this.RULE_STACK.length === 1 && isEndOfRule && expectTokAfterLastMatch === undefined) {\n    expectTokAfterLastMatch = EOF;\n    nextTokIdx = 1;\n  }\n  // We don't have anything to re-sync to...\n  // this condition was extracted from `shouldInRepetitionRecoveryBeTried` to act as a type-guard\n  if (expectTokAfterLastMatch === undefined || nextTokIdx === undefined) {\n    return;\n  }\n  if (this.shouldInRepetitionRecoveryBeTried(expectTokAfterLastMatch, nextTokIdx, notStuck)) {\n    // TODO: performance optimization: instead of passing the original args here, we modify\n    // the args param (or create a new one) and make sure the lookahead func is explicitly provided\n    // to avoid searching the cache for it once more.\n    this.tryInRepetitionRecovery(prodFunc, args, lookaheadFunc, expectTokAfterLastMatch);\n  }\n}", "map": {"version": 3, "names": ["createTokenInstance", "EOF", "tokenMatcher", "clone", "dropRight", "find", "flatten", "has", "includes", "isEmpty", "map", "MismatchedTokenException", "IN", "DEFAULT_PARSER_CONFIG", "EOF_FOLLOW_KEY", "IN_RULE_RECOVERY_EXCEPTION", "InRuleRecoveryException", "Error", "constructor", "message", "name", "Recoverable", "initRecoverable", "config", "firstAfterRepMap", "resyncFollows", "recoveryEnabled", "attemptInRepetitionRecovery", "getTokenToInsert", "tokType", "tokToInsert", "NaN", "isInsertedInRecovery", "canTokenTypeBeInsertedInRecovery", "canTokenTypeBeDeletedInRecovery", "tryInRepetitionRecovery", "grammarRule", "grammarRuleArgs", "lookAheadFunc", "expectedTokType", "reSyncTokType", "findReSyncTokenType", "savedLexerState", "exportLexerState", "resyncedTokens", "passedResyncPoint", "nextTokenWithoutResync", "LA", "currToken", "generateErrorMessage", "previousToken", "msg", "errorMessageProvider", "buildMismatchTokenMessage", "expected", "actual", "previous", "ruleName", "getCurrRuleFullName", "error", "SAVE_ERROR", "call", "apply", "SKIP_TOKEN", "addToResyncTokens", "importLexerState", "shouldInRepetitionRecoveryBeTried", "expectTokAfterLastMatch", "nextTokIdx", "notStuck", "isBackTracking", "canPerformInRuleRecovery", "getFollowsForInRuleRecovery", "tokIdxInRule", "grammarPath", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "follows", "getNextPossibleTokenTypes", "tryInRuleRecovery", "canRecoverWithSingleTokenInsertion", "canRecoverWithSingleTokenDeletion", "nextTok", "consumeToken", "expectedToken", "mismatchedTok", "isMisMatchedTokInFollows", "possibleFollowsTokType", "undefined", "isNextTokenWhatIsExpected", "isInCurrentRuleReSyncSet", "tokenTypeIdx", "<PERSON><PERSON><PERSON>", "getCurrFollowKey", "currentRuleReSyncSet", "getFollowSetFromFollowKey", "allPossibleReSyncTokTypes", "flattenFollowSet", "nextToken", "k", "foundMatch", "resyncTokType", "canMatch", "RULE_STACK", "length", "currRuleShortName", "getLastExplicitRuleShortName", "currRuleIdx", "getLastExplicitRuleOccurrenceIndex", "prevRuleShortName", "getPreviousExplicitRuleShortName", "shortRuleNameToFullName", "idxInCallingRule", "inRule", "buildFullFollowKeyStack", "explicitRuleStack", "explicitOccurrenceStack", "RULE_OCCURRENCE_STACK", "idx", "followStack", "curr<PERSON><PERSON>", "follow<PERSON>ame", "token", "resyncTokens", "push", "reSyncTo", "prodFunc", "args", "lookaheadFunc", "dslMethodIdx", "prodOccurrence", "nextToksWalker", "pathRuleStack", "getHumanReadableRuleStack", "pathOccurrenceStack", "ruleStack", "occurrenceStack", "lastTok", "lastTokOccurrence", "currShortName", "key", "getKeyForAutomaticLookahead", "firstAfterRepInfo", "currRuleName", "ruleGrammar", "getGAstProductions", "walker", "startWalking", "occurrence", "isEndOfRule"], "sources": ["C:\\Users\\<USER>\\FixTorchUMLDGM\\node_modules\\chevrotain\\src\\parse\\parser\\traits\\recoverable.ts"], "sourcesContent": ["import {\n  createTokenInstance,\n  EOF,\n  tokenMatcher,\n} from \"../../../scan/tokens_public.js\";\nimport {\n  AbstractNextTerminalAfterProductionWalker,\n  IFirstAfterRepetition,\n} from \"../../grammar/interpreter.js\";\nimport {\n  clone,\n  dropRight,\n  find,\n  flatten,\n  has,\n  includes,\n  isEmpty,\n  map,\n} from \"lodash-es\";\nimport {\n  IParserConfig,\n  IToken,\n  ITokenGrammarPath,\n  TokenType,\n} from \"@chevrotain/types\";\nimport { MismatchedTokenException } from \"../../exceptions_public.js\";\nimport { IN } from \"../../constants.js\";\nimport { MixedInParser } from \"./parser_traits.js\";\nimport { DEFAULT_PARSER_CONFIG } from \"../parser.js\";\n\nexport const EOF_FOLLOW_KEY: any = {};\n\nexport interface IFollowKey {\n  ruleName: string;\n  idxInCallingRule: number;\n  inRule: string;\n}\n\nexport const IN_RULE_RECOVERY_EXCEPTION = \"InRuleRecoveryException\";\n\nexport class InRuleRecoveryException extends Error {\n  constructor(message: string) {\n    super(message);\n    this.name = IN_RULE_RECOVERY_EXCEPTION;\n  }\n}\n\n/**\n * This trait is responsible for the error recovery and fault tolerant logic\n */\nexport class Recoverable {\n  recoveryEnabled: boolean;\n  firstAfterRepMap: Record<string, IFirstAfterRepetition>;\n  resyncFollows: Record<string, TokenType[]>;\n\n  initRecoverable(config: IParserConfig) {\n    this.firstAfterRepMap = {};\n    this.resyncFollows = {};\n\n    this.recoveryEnabled = has(config, \"recoveryEnabled\")\n      ? (config.recoveryEnabled as boolean) // assumes end user provides the correct config value/type\n      : DEFAULT_PARSER_CONFIG.recoveryEnabled;\n\n    // performance optimization, NOOP will be inlined which\n    // effectively means that this optional feature does not exist\n    // when not used.\n    if (this.recoveryEnabled) {\n      this.attemptInRepetitionRecovery = attemptInRepetitionRecovery;\n    }\n  }\n\n  public getTokenToInsert(tokType: TokenType): IToken {\n    const tokToInsert = createTokenInstance(\n      tokType,\n      \"\",\n      NaN,\n      NaN,\n      NaN,\n      NaN,\n      NaN,\n      NaN,\n    );\n    tokToInsert.isInsertedInRecovery = true;\n    return tokToInsert;\n  }\n\n  public canTokenTypeBeInsertedInRecovery(tokType: TokenType): boolean {\n    return true;\n  }\n\n  public canTokenTypeBeDeletedInRecovery(tokType: TokenType): boolean {\n    return true;\n  }\n\n  tryInRepetitionRecovery(\n    this: MixedInParser,\n    grammarRule: Function,\n    grammarRuleArgs: any[],\n    lookAheadFunc: () => boolean,\n    expectedTokType: TokenType,\n  ): void {\n    // TODO: can the resyncTokenType be cached?\n    const reSyncTokType = this.findReSyncTokenType();\n    const savedLexerState = this.exportLexerState();\n    const resyncedTokens: IToken[] = [];\n    let passedResyncPoint = false;\n\n    const nextTokenWithoutResync = this.LA(1);\n    let currToken = this.LA(1);\n\n    const generateErrorMessage = () => {\n      const previousToken = this.LA(0);\n      // we are preemptively re-syncing before an error has been detected, therefor we must reproduce\n      // the error that would have been thrown\n      const msg = this.errorMessageProvider.buildMismatchTokenMessage({\n        expected: expectedTokType,\n        actual: nextTokenWithoutResync,\n        previous: previousToken,\n        ruleName: this.getCurrRuleFullName(),\n      });\n      const error = new MismatchedTokenException(\n        msg,\n        nextTokenWithoutResync,\n        this.LA(0),\n      );\n      // the first token here will be the original cause of the error, this is not part of the resyncedTokens property.\n      error.resyncedTokens = dropRight(resyncedTokens);\n      this.SAVE_ERROR(error);\n    };\n\n    while (!passedResyncPoint) {\n      // re-synced to a point where we can safely exit the repetition/\n      if (this.tokenMatcher(currToken, expectedTokType)) {\n        generateErrorMessage();\n        return; // must return here to avoid reverting the inputIdx\n      } else if (lookAheadFunc.call(this)) {\n        // we skipped enough tokens so we can resync right back into another iteration of the repetition grammar rule\n        generateErrorMessage();\n        // recursive invocation in other to support multiple re-syncs in the same top level repetition grammar rule\n        grammarRule.apply(this, grammarRuleArgs);\n        return; // must return here to avoid reverting the inputIdx\n      } else if (this.tokenMatcher(currToken, reSyncTokType)) {\n        passedResyncPoint = true;\n      } else {\n        currToken = this.SKIP_TOKEN();\n        this.addToResyncTokens(currToken, resyncedTokens);\n      }\n    }\n\n    // we were unable to find a CLOSER point to resync inside the Repetition, reset the state.\n    // The parsing exception we were trying to prevent will happen in the NEXT parsing step. it may be handled by\n    // \"between rules\" resync recovery later in the flow.\n    this.importLexerState(savedLexerState);\n  }\n\n  shouldInRepetitionRecoveryBeTried(\n    this: MixedInParser,\n    expectTokAfterLastMatch: TokenType,\n    nextTokIdx: number,\n    notStuck: boolean | undefined,\n  ): boolean {\n    // Edge case of arriving from a MANY repetition which is stuck\n    // Attempting recovery in this case could cause an infinite loop\n    if (notStuck === false) {\n      return false;\n    }\n\n    // no need to recover, next token is what we expect...\n    if (this.tokenMatcher(this.LA(1), expectTokAfterLastMatch)) {\n      return false;\n    }\n\n    // error recovery is disabled during backtracking as it can make the parser ignore a valid grammar path\n    // and prefer some backtracking path that includes recovered errors.\n    if (this.isBackTracking()) {\n      return false;\n    }\n\n    // if we can perform inRule recovery (single token insertion or deletion) we always prefer that recovery algorithm\n    // because if it works, it makes the least amount of changes to the input stream (greedy algorithm)\n    //noinspection RedundantIfStatementJS\n    if (\n      this.canPerformInRuleRecovery(\n        expectTokAfterLastMatch,\n        this.getFollowsForInRuleRecovery(expectTokAfterLastMatch, nextTokIdx),\n      )\n    ) {\n      return false;\n    }\n\n    return true;\n  }\n\n  // Error Recovery functionality\n  getFollowsForInRuleRecovery(\n    this: MixedInParser,\n    tokType: TokenType,\n    tokIdxInRule: number,\n  ): TokenType[] {\n    const grammarPath = this.getCurrentGrammarPath(tokType, tokIdxInRule);\n    const follows = this.getNextPossibleTokenTypes(grammarPath);\n    return follows;\n  }\n\n  tryInRuleRecovery(\n    this: MixedInParser,\n    expectedTokType: TokenType,\n    follows: TokenType[],\n  ): IToken {\n    if (this.canRecoverWithSingleTokenInsertion(expectedTokType, follows)) {\n      const tokToInsert = this.getTokenToInsert(expectedTokType);\n      return tokToInsert;\n    }\n\n    if (this.canRecoverWithSingleTokenDeletion(expectedTokType)) {\n      const nextTok = this.SKIP_TOKEN();\n      this.consumeToken();\n      return nextTok;\n    }\n\n    throw new InRuleRecoveryException(\"sad sad panda\");\n  }\n\n  canPerformInRuleRecovery(\n    this: MixedInParser,\n    expectedToken: TokenType,\n    follows: TokenType[],\n  ): boolean {\n    return (\n      this.canRecoverWithSingleTokenInsertion(expectedToken, follows) ||\n      this.canRecoverWithSingleTokenDeletion(expectedToken)\n    );\n  }\n\n  canRecoverWithSingleTokenInsertion(\n    this: MixedInParser,\n    expectedTokType: TokenType,\n    follows: TokenType[],\n  ): boolean {\n    if (!this.canTokenTypeBeInsertedInRecovery(expectedTokType)) {\n      return false;\n    }\n\n    // must know the possible following tokens to perform single token insertion\n    if (isEmpty(follows)) {\n      return false;\n    }\n\n    const mismatchedTok = this.LA(1);\n    const isMisMatchedTokInFollows =\n      find(follows, (possibleFollowsTokType: TokenType) => {\n        return this.tokenMatcher(mismatchedTok, possibleFollowsTokType);\n      }) !== undefined;\n\n    return isMisMatchedTokInFollows;\n  }\n\n  canRecoverWithSingleTokenDeletion(\n    this: MixedInParser,\n    expectedTokType: TokenType,\n  ): boolean {\n    if (!this.canTokenTypeBeDeletedInRecovery(expectedTokType)) {\n      return false;\n    }\n\n    const isNextTokenWhatIsExpected = this.tokenMatcher(\n      this.LA(2),\n      expectedTokType,\n    );\n    return isNextTokenWhatIsExpected;\n  }\n\n  isInCurrentRuleReSyncSet(\n    this: MixedInParser,\n    tokenTypeIdx: TokenType,\n  ): boolean {\n    const followKey = this.getCurrFollowKey();\n    const currentRuleReSyncSet = this.getFollowSetFromFollowKey(followKey);\n    return includes(currentRuleReSyncSet, tokenTypeIdx);\n  }\n\n  findReSyncTokenType(this: MixedInParser): TokenType {\n    const allPossibleReSyncTokTypes = this.flattenFollowSet();\n    // this loop will always terminate as EOF is always in the follow stack and also always (virtually) in the input\n    let nextToken = this.LA(1);\n    let k = 2;\n    while (true) {\n      const foundMatch = find(allPossibleReSyncTokTypes, (resyncTokType) => {\n        const canMatch = tokenMatcher(nextToken, resyncTokType);\n        return canMatch;\n      });\n      if (foundMatch !== undefined) {\n        return foundMatch;\n      }\n      nextToken = this.LA(k);\n      k++;\n    }\n  }\n\n  getCurrFollowKey(this: MixedInParser): IFollowKey {\n    // the length is at least one as we always add the ruleName to the stack before invoking the rule.\n    if (this.RULE_STACK.length === 1) {\n      return EOF_FOLLOW_KEY;\n    }\n    const currRuleShortName = this.getLastExplicitRuleShortName();\n    const currRuleIdx = this.getLastExplicitRuleOccurrenceIndex();\n    const prevRuleShortName = this.getPreviousExplicitRuleShortName();\n\n    return {\n      ruleName: this.shortRuleNameToFullName(currRuleShortName),\n      idxInCallingRule: currRuleIdx,\n      inRule: this.shortRuleNameToFullName(prevRuleShortName),\n    };\n  }\n\n  buildFullFollowKeyStack(this: MixedInParser): IFollowKey[] {\n    const explicitRuleStack = this.RULE_STACK;\n    const explicitOccurrenceStack = this.RULE_OCCURRENCE_STACK;\n\n    return map(explicitRuleStack, (ruleName, idx) => {\n      if (idx === 0) {\n        return EOF_FOLLOW_KEY;\n      }\n      return {\n        ruleName: this.shortRuleNameToFullName(ruleName),\n        idxInCallingRule: explicitOccurrenceStack[idx],\n        inRule: this.shortRuleNameToFullName(explicitRuleStack[idx - 1]),\n      };\n    });\n  }\n\n  flattenFollowSet(this: MixedInParser): TokenType[] {\n    const followStack = map(this.buildFullFollowKeyStack(), (currKey) => {\n      return this.getFollowSetFromFollowKey(currKey);\n    });\n    return <any>flatten(followStack);\n  }\n\n  getFollowSetFromFollowKey(\n    this: MixedInParser,\n    followKey: IFollowKey,\n  ): TokenType[] {\n    if (followKey === EOF_FOLLOW_KEY) {\n      return [EOF];\n    }\n\n    const followName =\n      followKey.ruleName + followKey.idxInCallingRule + IN + followKey.inRule;\n\n    return this.resyncFollows[followName];\n  }\n\n  // It does not make any sense to include a virtual EOF token in the list of resynced tokens\n  // as EOF does not really exist and thus does not contain any useful information (line/column numbers)\n  addToResyncTokens(\n    this: MixedInParser,\n    token: IToken,\n    resyncTokens: IToken[],\n  ): IToken[] {\n    if (!this.tokenMatcher(token, EOF)) {\n      resyncTokens.push(token);\n    }\n    return resyncTokens;\n  }\n\n  reSyncTo(this: MixedInParser, tokType: TokenType): IToken[] {\n    const resyncedTokens: IToken[] = [];\n    let nextTok = this.LA(1);\n    while (this.tokenMatcher(nextTok, tokType) === false) {\n      nextTok = this.SKIP_TOKEN();\n      this.addToResyncTokens(nextTok, resyncedTokens);\n    }\n    // the last token is not part of the error.\n    return dropRight(resyncedTokens);\n  }\n\n  attemptInRepetitionRecovery(\n    this: MixedInParser,\n    prodFunc: Function,\n    args: any[],\n    lookaheadFunc: () => boolean,\n    dslMethodIdx: number,\n    prodOccurrence: number,\n    nextToksWalker: typeof AbstractNextTerminalAfterProductionWalker,\n    notStuck?: boolean,\n  ): void {\n    // by default this is a NO-OP\n    // The actual implementation is with the function(not method) below\n  }\n\n  getCurrentGrammarPath(\n    this: MixedInParser,\n    tokType: TokenType,\n    tokIdxInRule: number,\n  ): ITokenGrammarPath {\n    const pathRuleStack: string[] = this.getHumanReadableRuleStack();\n    const pathOccurrenceStack: number[] = clone(this.RULE_OCCURRENCE_STACK);\n    const grammarPath: any = {\n      ruleStack: pathRuleStack,\n      occurrenceStack: pathOccurrenceStack,\n      lastTok: tokType,\n      lastTokOccurrence: tokIdxInRule,\n    };\n\n    return grammarPath;\n  }\n  getHumanReadableRuleStack(this: MixedInParser): string[] {\n    return map(this.RULE_STACK, (currShortName) =>\n      this.shortRuleNameToFullName(currShortName),\n    );\n  }\n}\n\nexport function attemptInRepetitionRecovery(\n  this: MixedInParser,\n  prodFunc: Function,\n  args: any[],\n  lookaheadFunc: () => boolean,\n  dslMethodIdx: number,\n  prodOccurrence: number,\n  nextToksWalker: typeof AbstractNextTerminalAfterProductionWalker,\n  notStuck?: boolean,\n): void {\n  const key = this.getKeyForAutomaticLookahead(dslMethodIdx, prodOccurrence);\n  let firstAfterRepInfo = this.firstAfterRepMap[key];\n  if (firstAfterRepInfo === undefined) {\n    const currRuleName = this.getCurrRuleFullName();\n    const ruleGrammar = this.getGAstProductions()[currRuleName];\n    const walker: AbstractNextTerminalAfterProductionWalker =\n      new nextToksWalker(ruleGrammar, prodOccurrence);\n    firstAfterRepInfo = walker.startWalking();\n    this.firstAfterRepMap[key] = firstAfterRepInfo;\n  }\n\n  let expectTokAfterLastMatch = firstAfterRepInfo.token;\n  let nextTokIdx = firstAfterRepInfo.occurrence;\n  const isEndOfRule = firstAfterRepInfo.isEndOfRule;\n\n  // special edge case of a TOP most repetition after which the input should END.\n  // this will force an attempt for inRule recovery in that scenario.\n  if (\n    this.RULE_STACK.length === 1 &&\n    isEndOfRule &&\n    expectTokAfterLastMatch === undefined\n  ) {\n    expectTokAfterLastMatch = EOF;\n    nextTokIdx = 1;\n  }\n\n  // We don't have anything to re-sync to...\n  // this condition was extracted from `shouldInRepetitionRecoveryBeTried` to act as a type-guard\n  if (expectTokAfterLastMatch === undefined || nextTokIdx === undefined) {\n    return;\n  }\n\n  if (\n    this.shouldInRepetitionRecoveryBeTried(\n      expectTokAfterLastMatch,\n      nextTokIdx,\n      notStuck,\n    )\n  ) {\n    // TODO: performance optimization: instead of passing the original args here, we modify\n    // the args param (or create a new one) and make sure the lookahead func is explicitly provided\n    // to avoid searching the cache for it once more.\n    this.tryInRepetitionRecovery(\n      prodFunc,\n      args,\n      lookaheadFunc,\n      expectTokAfterLastMatch,\n    );\n  }\n}\n"], "mappings": "AAAA,SACEA,mBAAmB,EACnBC,GAAG,EACHC,YAAY,QACP,gCAAgC;AAKvC,SACEC,KAAK,EACLC,SAAS,EACTC,IAAI,EACJC,OAAO,EACPC,GAAG,EACHC,QAAQ,EACRC,OAAO,EACPC,GAAG,QACE,WAAW;AAOlB,SAASC,wBAAwB,QAAQ,4BAA4B;AACrE,SAASC,EAAE,QAAQ,oBAAoB;AAEvC,SAASC,qBAAqB,QAAQ,cAAc;AAEpD,OAAO,MAAMC,cAAc,GAAQ,EAAE;AAQrC,OAAO,MAAMC,0BAA0B,GAAG,yBAAyB;AAEnE,OAAM,MAAOC,uBAAwB,SAAQC,KAAK;EAChDC,YAAYC,OAAe;IACzB,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACC,IAAI,GAAGL,0BAA0B;EACxC;;AAGF;;;AAGA,OAAM,MAAOM,WAAW;EAKtBC,eAAeA,CAACC,MAAqB;IACnC,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,aAAa,GAAG,EAAE;IAEvB,IAAI,CAACC,eAAe,GAAGnB,GAAG,CAACgB,MAAM,EAAE,iBAAiB,CAAC,GAChDA,MAAM,CAACG,eAA2B,CAAC;IAAA,EACpCb,qBAAqB,CAACa,eAAe;IAEzC;IACA;IACA;IACA,IAAI,IAAI,CAACA,eAAe,EAAE;MACxB,IAAI,CAACC,2BAA2B,GAAGA,2BAA2B;;EAElE;EAEOC,gBAAgBA,CAACC,OAAkB;IACxC,MAAMC,WAAW,GAAG9B,mBAAmB,CACrC6B,OAAO,EACP,EAAE,EACFE,GAAG,EACHA,GAAG,EACHA,GAAG,EACHA,GAAG,EACHA,GAAG,EACHA,GAAG,CACJ;IACDD,WAAW,CAACE,oBAAoB,GAAG,IAAI;IACvC,OAAOF,WAAW;EACpB;EAEOG,gCAAgCA,CAACJ,OAAkB;IACxD,OAAO,IAAI;EACb;EAEOK,+BAA+BA,CAACL,OAAkB;IACvD,OAAO,IAAI;EACb;EAEAM,uBAAuBA,CAErBC,WAAqB,EACrBC,eAAsB,EACtBC,aAA4B,EAC5BC,eAA0B;IAE1B;IACA,MAAMC,aAAa,GAAG,IAAI,CAACC,mBAAmB,EAAE;IAChD,MAAMC,eAAe,GAAG,IAAI,CAACC,gBAAgB,EAAE;IAC/C,MAAMC,cAAc,GAAa,EAAE;IACnC,IAAIC,iBAAiB,GAAG,KAAK;IAE7B,MAAMC,sBAAsB,GAAG,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC;IACzC,IAAIC,SAAS,GAAG,IAAI,CAACD,EAAE,CAAC,CAAC,CAAC;IAE1B,MAAME,oBAAoB,GAAGA,CAAA,KAAK;MAChC,MAAMC,aAAa,GAAG,IAAI,CAACH,EAAE,CAAC,CAAC,CAAC;MAChC;MACA;MACA,MAAMI,GAAG,GAAG,IAAI,CAACC,oBAAoB,CAACC,yBAAyB,CAAC;QAC9DC,QAAQ,EAAEf,eAAe;QACzBgB,MAAM,EAAET,sBAAsB;QAC9BU,QAAQ,EAAEN,aAAa;QACvBO,QAAQ,EAAE,IAAI,CAACC,mBAAmB;OACnC,CAAC;MACF,MAAMC,KAAK,GAAG,IAAIhD,wBAAwB,CACxCwC,GAAG,EACHL,sBAAsB,EACtB,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC,CACX;MACD;MACAY,KAAK,CAACf,cAAc,GAAGxC,SAAS,CAACwC,cAAc,CAAC;MAChD,IAAI,CAACgB,UAAU,CAACD,KAAK,CAAC;IACxB,CAAC;IAED,OAAO,CAACd,iBAAiB,EAAE;MACzB;MACA,IAAI,IAAI,CAAC3C,YAAY,CAAC8C,SAAS,EAAET,eAAe,CAAC,EAAE;QACjDU,oBAAoB,EAAE;QACtB,OAAO,CAAC;OACT,MAAM,IAAIX,aAAa,CAACuB,IAAI,CAAC,IAAI,CAAC,EAAE;QACnC;QACAZ,oBAAoB,EAAE;QACtB;QACAb,WAAW,CAAC0B,KAAK,CAAC,IAAI,EAAEzB,eAAe,CAAC;QACxC,OAAO,CAAC;OACT,MAAM,IAAI,IAAI,CAACnC,YAAY,CAAC8C,SAAS,EAAER,aAAa,CAAC,EAAE;QACtDK,iBAAiB,GAAG,IAAI;OACzB,MAAM;QACLG,SAAS,GAAG,IAAI,CAACe,UAAU,EAAE;QAC7B,IAAI,CAACC,iBAAiB,CAAChB,SAAS,EAAEJ,cAAc,CAAC;;;IAIrD;IACA;IACA;IACA,IAAI,CAACqB,gBAAgB,CAACvB,eAAe,CAAC;EACxC;EAEAwB,iCAAiCA,CAE/BC,uBAAkC,EAClCC,UAAkB,EAClBC,QAA6B;IAE7B;IACA;IACA,IAAIA,QAAQ,KAAK,KAAK,EAAE;MACtB,OAAO,KAAK;;IAGd;IACA,IAAI,IAAI,CAACnE,YAAY,CAAC,IAAI,CAAC6C,EAAE,CAAC,CAAC,CAAC,EAAEoB,uBAAuB,CAAC,EAAE;MAC1D,OAAO,KAAK;;IAGd;IACA;IACA,IAAI,IAAI,CAACG,cAAc,EAAE,EAAE;MACzB,OAAO,KAAK;;IAGd;IACA;IACA;IACA,IACE,IAAI,CAACC,wBAAwB,CAC3BJ,uBAAuB,EACvB,IAAI,CAACK,2BAA2B,CAACL,uBAAuB,EAAEC,UAAU,CAAC,CACtE,EACD;MACA,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEA;EACAI,2BAA2BA,CAEzB3C,OAAkB,EAClB4C,YAAoB;IAEpB,MAAMC,WAAW,GAAG,IAAI,CAACC,qBAAqB,CAAC9C,OAAO,EAAE4C,YAAY,CAAC;IACrE,MAAMG,OAAO,GAAG,IAAI,CAACC,yBAAyB,CAACH,WAAW,CAAC;IAC3D,OAAOE,OAAO;EAChB;EAEAE,iBAAiBA,CAEfvC,eAA0B,EAC1BqC,OAAoB;IAEpB,IAAI,IAAI,CAACG,kCAAkC,CAACxC,eAAe,EAAEqC,OAAO,CAAC,EAAE;MACrE,MAAM9C,WAAW,GAAG,IAAI,CAACF,gBAAgB,CAACW,eAAe,CAAC;MAC1D,OAAOT,WAAW;;IAGpB,IAAI,IAAI,CAACkD,iCAAiC,CAACzC,eAAe,CAAC,EAAE;MAC3D,MAAM0C,OAAO,GAAG,IAAI,CAAClB,UAAU,EAAE;MACjC,IAAI,CAACmB,YAAY,EAAE;MACnB,OAAOD,OAAO;;IAGhB,MAAM,IAAIjE,uBAAuB,CAAC,eAAe,CAAC;EACpD;EAEAuD,wBAAwBA,CAEtBY,aAAwB,EACxBP,OAAoB;IAEpB,OACE,IAAI,CAACG,kCAAkC,CAACI,aAAa,EAAEP,OAAO,CAAC,IAC/D,IAAI,CAACI,iCAAiC,CAACG,aAAa,CAAC;EAEzD;EAEAJ,kCAAkCA,CAEhCxC,eAA0B,EAC1BqC,OAAoB;IAEpB,IAAI,CAAC,IAAI,CAAC3C,gCAAgC,CAACM,eAAe,CAAC,EAAE;MAC3D,OAAO,KAAK;;IAGd;IACA,IAAI9B,OAAO,CAACmE,OAAO,CAAC,EAAE;MACpB,OAAO,KAAK;;IAGd,MAAMQ,aAAa,GAAG,IAAI,CAACrC,EAAE,CAAC,CAAC,CAAC;IAChC,MAAMsC,wBAAwB,GAC5BhF,IAAI,CAACuE,OAAO,EAAGU,sBAAiC,IAAI;MAClD,OAAO,IAAI,CAACpF,YAAY,CAACkF,aAAa,EAAEE,sBAAsB,CAAC;IACjE,CAAC,CAAC,KAAKC,SAAS;IAElB,OAAOF,wBAAwB;EACjC;EAEAL,iCAAiCA,CAE/BzC,eAA0B;IAE1B,IAAI,CAAC,IAAI,CAACL,+BAA+B,CAACK,eAAe,CAAC,EAAE;MAC1D,OAAO,KAAK;;IAGd,MAAMiD,yBAAyB,GAAG,IAAI,CAACtF,YAAY,CACjD,IAAI,CAAC6C,EAAE,CAAC,CAAC,CAAC,EACVR,eAAe,CAChB;IACD,OAAOiD,yBAAyB;EAClC;EAEAC,wBAAwBA,CAEtBC,YAAuB;IAEvB,MAAMC,SAAS,GAAG,IAAI,CAACC,gBAAgB,EAAE;IACzC,MAAMC,oBAAoB,GAAG,IAAI,CAACC,yBAAyB,CAACH,SAAS,CAAC;IACtE,OAAOnF,QAAQ,CAACqF,oBAAoB,EAAEH,YAAY,CAAC;EACrD;EAEAjD,mBAAmBA,CAAA;IACjB,MAAMsD,yBAAyB,GAAG,IAAI,CAACC,gBAAgB,EAAE;IACzD;IACA,IAAIC,SAAS,GAAG,IAAI,CAAClD,EAAE,CAAC,CAAC,CAAC;IAC1B,IAAImD,CAAC,GAAG,CAAC;IACT,OAAO,IAAI,EAAE;MACX,MAAMC,UAAU,GAAG9F,IAAI,CAAC0F,yBAAyB,EAAGK,aAAa,IAAI;QACnE,MAAMC,QAAQ,GAAGnG,YAAY,CAAC+F,SAAS,EAAEG,aAAa,CAAC;QACvD,OAAOC,QAAQ;MACjB,CAAC,CAAC;MACF,IAAIF,UAAU,KAAKZ,SAAS,EAAE;QAC5B,OAAOY,UAAU;;MAEnBF,SAAS,GAAG,IAAI,CAAClD,EAAE,CAACmD,CAAC,CAAC;MACtBA,CAAC,EAAE;;EAEP;EAEAN,gBAAgBA,CAAA;IACd;IACA,IAAI,IAAI,CAACU,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;MAChC,OAAOzF,cAAc;;IAEvB,MAAM0F,iBAAiB,GAAG,IAAI,CAACC,4BAA4B,EAAE;IAC7D,MAAMC,WAAW,GAAG,IAAI,CAACC,kCAAkC,EAAE;IAC7D,MAAMC,iBAAiB,GAAG,IAAI,CAACC,gCAAgC,EAAE;IAEjE,OAAO;MACLpD,QAAQ,EAAE,IAAI,CAACqD,uBAAuB,CAACN,iBAAiB,CAAC;MACzDO,gBAAgB,EAAEL,WAAW;MAC7BM,MAAM,EAAE,IAAI,CAACF,uBAAuB,CAACF,iBAAiB;KACvD;EACH;EAEAK,uBAAuBA,CAAA;IACrB,MAAMC,iBAAiB,GAAG,IAAI,CAACZ,UAAU;IACzC,MAAMa,uBAAuB,GAAG,IAAI,CAACC,qBAAqB;IAE1D,OAAO1G,GAAG,CAACwG,iBAAiB,EAAE,CAACzD,QAAQ,EAAE4D,GAAG,KAAI;MAC9C,IAAIA,GAAG,KAAK,CAAC,EAAE;QACb,OAAOvG,cAAc;;MAEvB,OAAO;QACL2C,QAAQ,EAAE,IAAI,CAACqD,uBAAuB,CAACrD,QAAQ,CAAC;QAChDsD,gBAAgB,EAAEI,uBAAuB,CAACE,GAAG,CAAC;QAC9CL,MAAM,EAAE,IAAI,CAACF,uBAAuB,CAACI,iBAAiB,CAACG,GAAG,GAAG,CAAC,CAAC;OAChE;IACH,CAAC,CAAC;EACJ;EAEArB,gBAAgBA,CAAA;IACd,MAAMsB,WAAW,GAAG5G,GAAG,CAAC,IAAI,CAACuG,uBAAuB,EAAE,EAAGM,OAAO,IAAI;MAClE,OAAO,IAAI,CAACzB,yBAAyB,CAACyB,OAAO,CAAC;IAChD,CAAC,CAAC;IACF,OAAYjH,OAAO,CAACgH,WAAW,CAAC;EAClC;EAEAxB,yBAAyBA,CAEvBH,SAAqB;IAErB,IAAIA,SAAS,KAAK7E,cAAc,EAAE;MAChC,OAAO,CAACb,GAAG,CAAC;;IAGd,MAAMuH,UAAU,GACd7B,SAAS,CAAClC,QAAQ,GAAGkC,SAAS,CAACoB,gBAAgB,GAAGnG,EAAE,GAAG+E,SAAS,CAACqB,MAAM;IAEzE,OAAO,IAAI,CAACvF,aAAa,CAAC+F,UAAU,CAAC;EACvC;EAEA;EACA;EACAxD,iBAAiBA,CAEfyD,KAAa,EACbC,YAAsB;IAEtB,IAAI,CAAC,IAAI,CAACxH,YAAY,CAACuH,KAAK,EAAExH,GAAG,CAAC,EAAE;MAClCyH,YAAY,CAACC,IAAI,CAACF,KAAK,CAAC;;IAE1B,OAAOC,YAAY;EACrB;EAEAE,QAAQA,CAAsB/F,OAAkB;IAC9C,MAAMe,cAAc,GAAa,EAAE;IACnC,IAAIqC,OAAO,GAAG,IAAI,CAAClC,EAAE,CAAC,CAAC,CAAC;IACxB,OAAO,IAAI,CAAC7C,YAAY,CAAC+E,OAAO,EAAEpD,OAAO,CAAC,KAAK,KAAK,EAAE;MACpDoD,OAAO,GAAG,IAAI,CAAClB,UAAU,EAAE;MAC3B,IAAI,CAACC,iBAAiB,CAACiB,OAAO,EAAErC,cAAc,CAAC;;IAEjD;IACA,OAAOxC,SAAS,CAACwC,cAAc,CAAC;EAClC;EAEAjB,2BAA2BA,CAEzBkG,QAAkB,EAClBC,IAAW,EACXC,aAA4B,EAC5BC,YAAoB,EACpBC,cAAsB,EACtBC,cAAgE,EAChE7D,QAAkB;IAElB;IACA;EAAA;EAGFM,qBAAqBA,CAEnB9C,OAAkB,EAClB4C,YAAoB;IAEpB,MAAM0D,aAAa,GAAa,IAAI,CAACC,yBAAyB,EAAE;IAChE,MAAMC,mBAAmB,GAAalI,KAAK,CAAC,IAAI,CAACiH,qBAAqB,CAAC;IACvE,MAAM1C,WAAW,GAAQ;MACvB4D,SAAS,EAAEH,aAAa;MACxBI,eAAe,EAAEF,mBAAmB;MACpCG,OAAO,EAAE3G,OAAO;MAChB4G,iBAAiB,EAAEhE;KACpB;IAED,OAAOC,WAAW;EACpB;EACA0D,yBAAyBA,CAAA;IACvB,OAAO1H,GAAG,CAAC,IAAI,CAAC4F,UAAU,EAAGoC,aAAa,IACxC,IAAI,CAAC5B,uBAAuB,CAAC4B,aAAa,CAAC,CAC5C;EACH;;AAGF,OAAM,SAAU/G,2BAA2BA,CAEzCkG,QAAkB,EAClBC,IAAW,EACXC,aAA4B,EAC5BC,YAAoB,EACpBC,cAAsB,EACtBC,cAAgE,EAChE7D,QAAkB;EAElB,MAAMsE,GAAG,GAAG,IAAI,CAACC,2BAA2B,CAACZ,YAAY,EAAEC,cAAc,CAAC;EAC1E,IAAIY,iBAAiB,GAAG,IAAI,CAACrH,gBAAgB,CAACmH,GAAG,CAAC;EAClD,IAAIE,iBAAiB,KAAKtD,SAAS,EAAE;IACnC,MAAMuD,YAAY,GAAG,IAAI,CAACpF,mBAAmB,EAAE;IAC/C,MAAMqF,WAAW,GAAG,IAAI,CAACC,kBAAkB,EAAE,CAACF,YAAY,CAAC;IAC3D,MAAMG,MAAM,GACV,IAAIf,cAAc,CAACa,WAAW,EAAEd,cAAc,CAAC;IACjDY,iBAAiB,GAAGI,MAAM,CAACC,YAAY,EAAE;IACzC,IAAI,CAAC1H,gBAAgB,CAACmH,GAAG,CAAC,GAAGE,iBAAiB;;EAGhD,IAAI1E,uBAAuB,GAAG0E,iBAAiB,CAACpB,KAAK;EACrD,IAAIrD,UAAU,GAAGyE,iBAAiB,CAACM,UAAU;EAC7C,MAAMC,WAAW,GAAGP,iBAAiB,CAACO,WAAW;EAEjD;EACA;EACA,IACE,IAAI,CAAC9C,UAAU,CAACC,MAAM,KAAK,CAAC,IAC5B6C,WAAW,IACXjF,uBAAuB,KAAKoB,SAAS,EACrC;IACApB,uBAAuB,GAAGlE,GAAG;IAC7BmE,UAAU,GAAG,CAAC;;EAGhB;EACA;EACA,IAAID,uBAAuB,KAAKoB,SAAS,IAAInB,UAAU,KAAKmB,SAAS,EAAE;IACrE;;EAGF,IACE,IAAI,CAACrB,iCAAiC,CACpCC,uBAAuB,EACvBC,UAAU,EACVC,QAAQ,CACT,EACD;IACA;IACA;IACA;IACA,IAAI,CAAClC,uBAAuB,CAC1B0F,QAAQ,EACRC,IAAI,EACJC,aAAa,EACb5D,uBAAuB,CACxB;;AAEL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}