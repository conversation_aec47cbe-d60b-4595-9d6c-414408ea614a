{"ast": null, "code": "import { topsort, CycleException } from './topsort.js';\nexport { isAcyclic };\nfunction isAcyclic(g) {\n  try {\n    topsort(g);\n  } catch (e) {\n    if (e instanceof CycleException) {\n      return false;\n    }\n    throw e;\n  }\n  return true;\n}", "map": {"version": 3, "names": ["topsort", "CycleException", "isAcyclic", "g", "e"], "sources": ["C:/Users/<USER>/FixTorchUMLDGM/node_modules/dagre-d3-es/src/graphlib/alg/is-acyclic.js"], "sourcesContent": ["import { topsort, CycleException } from './topsort.js';\n\nexport { isAcyclic };\n\nfunction isAcyclic(g) {\n  try {\n    topsort(g);\n  } catch (e) {\n    if (e instanceof CycleException) {\n      return false;\n    }\n    throw e;\n  }\n  return true;\n}\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,cAAc,QAAQ,cAAc;AAEtD,SAASC,SAAS;AAElB,SAASA,SAASA,CAACC,CAAC,EAAE;EACpB,IAAI;IACFH,OAAO,CAACG,CAAC,CAAC;EACZ,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,IAAIA,CAAC,YAAYH,cAAc,EAAE;MAC/B,OAAO,KAAK;IACd;IACA,MAAMG,CAAC;EACT;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}